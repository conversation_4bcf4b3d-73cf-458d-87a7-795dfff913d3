#!/usr/bin/env python3
"""
提取Office文档哈希的简单脚本
"""

import struct
import os

def main():
    filename = "Final Exam(MS).docx"
    
    print(f"分析文件: {filename}")
    
    if not os.path.exists(filename):
        print(f"错误：文件 {filename} 不存在")
        return
    
    with open(filename, 'rb') as f:
        # 读取文件头
        header = f.read(512)
        
        # 检查OLE签名
        if header[:8] != b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1':
            print("错误：不是有效的OLE文件")
            return
        
        print("✓ 检测到OLE文件格式")
        
        # 解析基本头部信息
        sector_size = 2 ** struct.unpack('<H', header[30:32])[0]
        dir_first_sector = struct.unpack('<L', header[52:56])[0]
        
        print(f"扇区大小: {sector_size}")
        print(f"目录第一扇区: {dir_first_sector}")
        
        # 搜索加密信息
        print("\n搜索加密信息...")
        
        # 方法1：搜索已知的加密签名
        f.seek(0)
        file_size = f.seek(0, 2)
        f.seek(0)
        
        encryption_signatures = [
            (b'\x02\x00\x00\x00', "Office 2007"),
            (b'\x03\x00\x00\x00', "Office 2010"), 
            (b'\x04\x00\x00\x00', "Office 2013+"),
            (b'\x01\x00\x00\x00', "Office 97-2003"),
        ]
        
        found_encryption = False
        
        # 搜索前1MB的数据
        chunk_size = 8192
        for offset in range(0, min(file_size, 1024*1024), chunk_size):
            f.seek(offset)
            chunk = f.read(chunk_size)
            
            for sig, version in encryption_signatures:
                pos = chunk.find(sig)
                if pos != -1:
                    print(f"✓ 在偏移 {offset + pos} 找到 {version} 加密签名")
                    
                    # 读取加密数据
                    f.seek(offset + pos)
                    enc_data = f.read(256)
                    
                    print(f"加密数据 (前64字节): {enc_data[:64].hex()}")
                    
                    # 尝试提取关键信息
                    if len(enc_data) >= 32:
                        version_num = struct.unpack('<L', sig)[0]
                        
                        # 根据不同版本提取不同的信息
                        if version_num in [2, 3, 4]:  # Office 2007+
                            # 尝试提取盐值和验证器
                            if len(enc_data) >= 64:
                                salt_start = 8
                                salt_end = salt_start + 16
                                verifier_start = salt_end + 4
                                verifier_end = verifier_start + 16
                                
                                if verifier_end <= len(enc_data):
                                    salt = enc_data[salt_start:salt_end]
                                    verifier = enc_data[verifier_start:verifier_end]
                                    
                                    # 创建John the Ripper格式的哈希
                                    hash_str = f"{filename}:$office$*{version_num}*20*128*16*{salt.hex()}*{verifier.hex()}*{enc_data[:16].hex()}"
                                    
                                    print(f"\n✓ 提取的哈希:")
                                    print(hash_str)
                                    
                                    # 保存到文件
                                    with open("hash.txt", "w") as hash_file:
                                        hash_file.write(hash_str + "\n")
                                    
                                    print(f"\n✓ 哈希已保存到 hash.txt")
                                    print("现在可以使用 John the Ripper 破解:")
                                    print("john --wordlist=wordlist.txt hash.txt")
                                    
                                    found_encryption = True
                                    break
            
            if found_encryption:
                break
        
        if not found_encryption:
            print("❌ 未找到标准的加密签名")
            
            # 尝试其他方法
            print("\n尝试其他方法...")
            
            # 搜索可能的加密相关字符串
            f.seek(0)
            data = f.read(min(file_size, 1024*1024))
            
            encryption_keywords = [
                b'EncryptionInfo',
                b'EncryptedPackage', 
                b'DataSpaceMap',
                b'Version',
                b'Microsoft.Container'
            ]
            
            for keyword in encryption_keywords:
                pos = data.find(keyword)
                if pos != -1:
                    print(f"找到加密关键字 '{keyword.decode('ascii', errors='ignore')}' 在偏移 {pos}")
                    
                    # 读取周围的数据
                    start = max(0, pos - 64)
                    end = min(len(data), pos + 128)
                    context = data[start:end]
                    
                    print(f"上下文数据: {context.hex()}")
            
            # 如果还是没找到，尝试使用我们之前发现的数据
            print("\n使用之前发现的数据模式...")
            
            # 基于我们之前的分析，尝试构造一个哈希
            # 使用第一个数据块的内容
            first_block_offset = 512 + 30 * 512  # 第一个数据块
            f.seek(first_block_offset)
            first_block = f.read(31)
            
            if len(first_block) >= 16:
                # 创建一个基于发现数据的哈希
                pseudo_salt = first_block[:16]
                pseudo_verifier = first_block[16:] + b'\x00' * (16 - len(first_block[16:]))
                
                hash_str = f"{filename}:$office$*2007*20*128*16*{pseudo_salt.hex()}*{pseudo_verifier.hex()}*{first_block[:16].hex()}"
                
                print(f"\n基于发现数据的哈希:")
                print(hash_str)
                
                with open("hash_experimental.txt", "w") as hash_file:
                    hash_file.write(hash_str + "\n")
                
                print("实验性哈希已保存到 hash_experimental.txt")

if __name__ == "__main__":
    main()
