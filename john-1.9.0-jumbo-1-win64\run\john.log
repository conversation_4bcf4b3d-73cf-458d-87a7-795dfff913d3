0:00:00:00 Starting a new session
0:00:00:00 Loaded a total of 1 password hash
0:00:00:00 Cost 1 (MS Office version) is 2013 for all loaded hashes
0:00:00:00 Cost 2 (iteration count) is 100000 for all loaded hashes
0:00:00:00 Command line: /run/john --wordlist=wordlist.txt office_hash.txt 
0:00:00:00 - UTF-8 input encoding enabled
0:00:00:00 - Passwords will be stored UTF-8 encoded in .pot file
0:00:00:00 - Hash type: Office, 2007/2010/2013 (min-len 0, max-len 41 [worst case UTF-8] to 125 [ASCII])
0:00:00:00 - Algorithm: SHA1 256/256 AVX2 8x / SHA512 256/256 AVX2 4x AES
0:00:00:00 - Will reject candidates longer than 125 bytes
0:00:00:00 - Candidate passwords will be buffered and tried in chunks of 160
0:00:00:00 Proceeding with wordlist mode
0:00:00:00 - Wordlist file: wordlist.txt
0:00:00:00 - memory mapping wordlist (2282 bytes)
0:00:00:00 - No word mangling rules
0:00:00:00 - No stacked rules
0:00:00:00 Session completed
0:00:00:00 Starting a new session
0:00:00:00 Loaded a total of 1 password hash
0:00:00:00 Cost 1 (MS Office version) is 2013 for all loaded hashes
0:00:00:00 Cost 2 (iteration count) is 100000 for all loaded hashes
0:00:00:00 Command line: /run/john --wordlist=common_passwords.txt office_hash.txt 
0:00:00:00 - UTF-8 input encoding enabled
0:00:00:00 - Passwords will be stored UTF-8 encoded in .pot file
0:00:00:00 - Hash type: Office, 2007/2010/2013 (min-len 0, max-len 41 [worst case UTF-8] to 125 [ASCII])
0:00:00:00 - Algorithm: SHA1 256/256 AVX2 8x / SHA512 256/256 AVX2 4x AES
0:00:00:00 - Will reject candidates longer than 125 bytes
0:00:00:00 - Candidate passwords will be buffered and tried in chunks of 160
0:00:00:00 Proceeding with wordlist mode
0:00:00:00 - Wordlist file: common_passwords.txt
0:00:00:00 - memory mapping wordlist (2912 bytes)
0:00:00:00 - No word mangling rules
0:00:00:00 - No stacked rules
0:00:00:00 Session completed
0:00:00:00 Starting a new session
0:00:00:00 Loaded a total of 1 password hash
0:00:00:00 Cost 1 (MS Office version) is 2013 for all loaded hashes
0:00:00:00 Cost 2 (iteration count) is 100000 for all loaded hashes
0:00:00:00 Command line: /run/john --incremental office_hash.txt 
0:00:00:00 - UTF-8 input encoding enabled
0:00:00:00 - Passwords will be stored UTF-8 encoded in .pot file
0:00:00:00 - Hash type: Office, 2007/2010/2013 (min-len 0, max-len 41 [worst case UTF-8] to 125 [ASCII])
0:00:00:00 - Algorithm: SHA1 256/256 AVX2 8x / SHA512 256/256 AVX2 4x AES
0:00:00:00 - Will reject candidates longer than 125 bytes
0:00:00:00 - Candidate passwords will be buffered and tried in chunks of 160
0:00:00:00 Proceeding with "incremental" mode: ASCII
0:00:00:00 - Lengths 0 to 13, up to 95 different characters
0:00:00:00 Starting a new session
0:00:00:00 Loaded a total of 1 password hash
0:00:00:00 Cost 1 (MS Office version) is 2013 for all loaded hashes
0:00:00:00 Cost 2 (iteration count) is 100000 for all loaded hashes
0:00:00:00 Command line: /run/john --wordlist=targeted_passwords.txt office_hash.txt 
0:00:00:00 - UTF-8 input encoding enabled
0:00:00:00 - Passwords will be stored UTF-8 encoded in .pot file
0:00:00:00 - Hash type: Office, 2007/2010/2013 (min-len 0, max-len 41 [worst case UTF-8] to 125 [ASCII])
0:00:00:00 - Algorithm: SHA1 256/256 AVX2 8x / SHA512 256/256 AVX2 4x AES
0:00:00:00 - Will reject candidates longer than 125 bytes
0:00:00:00 - Candidate passwords will be buffered and tried in chunks of 160
0:00:00:00 Proceeding with wordlist mode
0:00:00:00 - Wordlist file: targeted_passwords.txt
0:00:00:00 - memory mapping wordlist (3240 bytes)
0:00:00:00 - No word mangling rules
0:00:00:00 - No stacked rules
0:00:00:00 Session completed
0:00:00:00 Starting a new session
0:00:00:00 Loaded a total of 1 password hash
0:00:00:00 Cost 1 (MS Office version) is 2013 for all loaded hashes
0:00:00:00 Cost 2 (iteration count) is 100000 for all loaded hashes
0:00:00:00 Command line: /run/john --wordlist=john-1.9.0-jumbo-1-win64/run/password.lst --rules office_hash.txt 
0:00:00:00 - UTF-8 input encoding enabled
0:00:00:00 - Passwords will be stored UTF-8 encoded in .pot file
0:00:00:00 - Hash type: Office, 2007/2010/2013 (min-len 0, max-len 41 [worst case UTF-8] to 125 [ASCII])
0:00:00:00 - Algorithm: SHA1 256/256 AVX2 8x / SHA512 256/256 AVX2 4x AES
0:00:00:00 - Will reject candidates longer than 125 bytes
0:00:00:00 - Candidate passwords will be buffered and tried in chunks of 160
0:00:00:00 Proceeding with wordlist mode
0:00:00:00 - Rules: Wordlist
0:00:00:00 - Wordlist file: john-1.9.0-jumbo-1-win64/run/password.lst
0:00:00:00 - memory mapping wordlist (29884 bytes)
0:00:00:00 - loading wordfile john-1.9.0-jumbo-1-win64/run/password.lst into memory (29884 bytes, max_size=2147483648)
0:00:00:00 - wordfile had 3559 lines and required 28472 bytes for index.
0:00:00:00 - suppressed 13 duplicate lines and/or comments from wordlist.
0:00:00:00 - 57 preprocessed word mangling rules
0:00:00:00 - No stacked rules
0:00:00:00 - Rule #1: ':' accepted as ''
0:00:00:06 - Rule #2: '-c >3 !?X l Q' accepted as '>3!?XlQ'
0:00:00:06 - Rule #3: '-c (?a >2 !?X c Q' accepted as '(?a>2!?XcQ'
0:00:00:12 - Rule #4: '<* >2 !?A l p' accepted as '<*>2!?Alp'
0:00:00:18 - Rule #5: '<* >2 !?A l $1' accepted as '<*>2!?Al$1'
0:00:00:23 - Rule #6: '-c <* >2 !?A c $1' accepted as '<*>2!?Ac$1'
0:00:00:29 - Rule #7: '<7 >1 !?A l d' accepted as '<7>1!?Ald'
0:00:00:33 - Rule #8: '>3 !?A l M r Q' accepted as '>3!?AlMrQ'
0:00:00:38 - Rule #9: '>2 !?A l ^1' accepted as '>2!?Al^1'
0:00:00:43 - Rule #10: '-c >2 !?X u Q M c Q u' accepted as '>2!?XuQMcQu'
0:00:00:49 - Rule #11: '<* >2 !?A l $2' accepted as '<*>2!?Al$2'
0:00:00:54 - Rule #12: '<* >2 !?A l $!' accepted as '<*>2!?Al$!'
0:00:00:59 - Rule #13: '<* >2 !?A l $3' accepted as '<*>2!?Al$3'
0:00:01:05 - Rule #14: '<* >2 !?A l $7' accepted as '<*>2!?Al$7'
0:00:01:10 - Rule #15: '<* >2 !?A l $9' accepted as '<*>2!?Al$9'
0:00:01:15 - Rule #16: '<* >2 !?A l $5' accepted as '<*>2!?Al$5'
0:00:01:21 - Rule #17: '<* >2 !?A l $4' accepted as '<*>2!?Al$4'
0:00:01:26 - Rule #18: '<* >2 !?A l $8' accepted as '<*>2!?Al$8'
0:00:01:31 - Rule #19: '<* >2 !?A l $6' accepted as '<*>2!?Al$6'
0:00:01:36 - Rule #20: '<* >2 !?A l $0' accepted as '<*>2!?Al$0'
0:00:01:42 - Rule #21: '<* >2 !?A l $.' accepted as '<*>2!?Al$.'
0:00:01:48 - Rule #22: '<* >2 !?A l $?' accepted as '<*>2!?Al$?'
0:00:01:53 - Rule #23: '/?p @?p >3 l' accepted as '/?p@?p>3l'
0:00:01:53 - Rule #24: '/?v @?v >3 l' accepted as '/?v@?v>3l'
0:00:01:57 - Rule #25: '/?w @?w >3 l' accepted as '/?w@?w>3l'
0:00:01:57 - Rule #26: '-c <7 >1 !?A c d' accepted as '<7>1!?Acd'
0:00:02:01 - Rule #27: '-c <+ >2 !?A c r' accepted as '<+>2!?Acr'
0:00:02:06 - Rule #28: '-c >2 !?A l M r Q c' accepted as '>2!?AlMrQc'
0:00:02:09 Session aborted
0:00:00:00 Starting a new session
0:00:00:00 Loaded a total of 1 password hash
0:00:00:00 Cost 1 (MS Office version) is 2013 for all loaded hashes
0:00:00:00 Cost 2 (iteration count) is 100000 for all loaded hashes
0:00:00:00 Command line: /run/john --wordlist=smart_passwords.txt --rules=best64 office_hash.txt 
0:00:00:00 - UTF-8 input encoding enabled
0:00:00:00 - Passwords will be stored UTF-8 encoded in .pot file
0:00:00:00 - Hash type: Office, 2007/2010/2013 (min-len 0, max-len 41 [worst case UTF-8] to 125 [ASCII])
0:00:00:00 - Algorithm: SHA1 256/256 AVX2 8x / SHA512 256/256 AVX2 4x AES
0:00:00:00 - Will reject candidates longer than 125 bytes
0:00:00:00 - Candidate passwords will be buffered and tried in chunks of 160
0:00:00:00 Proceeding with wordlist mode
0:00:00:00 - Rules: best64
0:00:00:00 - Wordlist file: smart_passwords.txt
0:00:00:00 - memory mapping wordlist (2153 bytes)
0:00:00:00 - loading wordfile smart_passwords.txt into memory (2153 bytes, max_size=2147483648)
0:00:00:00 - wordfile had 312 lines and required 2496 bytes for index.
0:00:00:00 - 79 preprocessed word mangling rules
0:00:00:00 - No stacked rules
0:00:00:00 - Rule #2: ':' accepted as ''
0:00:00:00 - Rule #3: 'r' accepted
0:00:00:00 - Rule #4: 'u' accepted
0:00:00:01 - Rule #5: 'T0' accepted
0:00:00:01 - Rule #6: '$0' accepted
0:00:00:02 - Rule #7: '$1' accepted
0:00:00:03 - Rule #8: '$2' accepted
0:00:00:03 - Rule #9: '$3' accepted
0:00:00:04 - Rule #10: '$4' accepted
0:00:00:04 - Rule #11: '$5' accepted
0:00:00:05 - Rule #12: '$6' accepted
0:00:00:06 - Rule #13: '$7' accepted
0:00:00:06 - Rule #14: '$8' accepted
0:00:00:07 - Rule #15: '$9' accepted
0:00:00:07 - Rule #16: '$0 $0' accepted as '$0$0'
0:00:00:08 - Rule #17: '$0 $1' accepted as '$0$1'
0:00:00:08 - Rule #18: '$0 $2' accepted as '$0$2'
0:00:00:09 - Rule #19: '$1 $1' accepted as '$1$1'
0:00:00:09 - Rule #20: '$1 $2' accepted as '$1$2'
0:00:00:10 - Rule #21: '$1 $3' accepted as '$1$3'
0:00:00:10 - Rule #22: '$2 $1' accepted as '$2$1'
0:00:00:11 - Rule #23: '$2 $2' accepted as '$2$2'
0:00:00:11 - Rule #24: '$2 $3' accepted as '$2$3'
0:00:00:12 - Rule #25: '$6 $9' accepted as '$6$9'
0:00:00:13 - Rule #26: '$7 $7' accepted as '$7$7'
0:00:00:13 - Rule #27: '$8 $8' accepted as '$8$8'
0:00:00:14 - Rule #28: '$9 $9' accepted as '$9$9'
0:00:00:14 - Rule #29: '$1 $2 $3' accepted as '$1$2$3'
0:00:00:15 - Rule #30: '$e' accepted
0:00:00:16 - Rule #31: '$s' accepted
0:00:00:16 - Rule #32: '] $a' accepted as ']$a'
0:00:00:17 - Rule #33: '] ] $s' accepted as ']]$s'
0:00:00:17 - Rule #34: '] ] $a' accepted as ']]$a'
0:00:00:18 - Rule #35: '] ] $e $r' accepted as ']]$e$r'
0:00:00:19 - Rule #36: '] ] $i $e' accepted as ']]$i$e'
0:00:00:19 - Rule #37: '] ] ] $o' accepted as ']]]$o'
0:00:00:19 - Rule #38: '] ] ] $y' accepted as ']]]$y'
0:00:00:20 - Rule #39: '] ] ] $1 $2 $3' accepted as ']]]$1$2$3'
0:00:00:21 - Rule #40: '] ] ] $m $a $n' accepted as ']]]$m$a$n'
0:00:00:21 - Rule #41: '] ] ] $d $o $g' accepted as ']]]$d$o$g'
0:00:00:22 - Rule #42: '^1' accepted
0:00:00:22 - Rule #43: '^e ^h ^t' accepted as '^e^h^t'
0:00:00:23 - Rule #44: 'o0d' accepted
0:00:00:23 - Rule #45: 'o0m o1a' accepted as 'o0mo1a'
0:00:00:24 - Rule #46: 'so0' accepted
0:00:00:25 - Rule #47: 'si1' accepted
0:00:00:25 - Rule #48: 'se3' accepted
0:00:00:26 - Rule #49: 'D2' accepted
0:00:00:26 - Rule #50: 'D2 D2' accepted as 'D2D2'
0:00:00:27 - Rule #51: 'D3' accepted
0:00:00:27 - Rule #52: 'D4' accepted
0:00:00:28 - Rule #53: ''5 D3' accepted as ''5D3'
0:00:00:28 - Rule #54: ''5 $1' accepted as ''5$1'
0:00:00:29 - Rule #55: ']' accepted
0:00:00:29 - Rule #56: '] ]' accepted as ']]'
0:00:00:30 - Rule #57: '] ] ]' accepted as ']]]'
0:00:00:31 - Rule #58: '] ] ] d' accepted as ']]]d'
0:00:00:31 - Rule #59: '] ] D1 ]' accepted as ']]D1]'
0:00:00:32 - Rule #60: '+5 ] } } } } '4' accepted as '+5]}}}}'4'
0:00:00:32 - Rule #61: 'O02 { { { { { {' accepted as 'O02{{{{{{'
0:00:00:33 - Rule #62: '} ] ] {' accepted as '}]]{'
0:00:00:33 - Rule #63: '} } -0 O12' accepted as '}}-0O12'
0:00:00:34 - Rule #64: '} } }' accepted as '}}}'
0:00:00:34 - Rule #65: '} } } } '4' accepted as '}}}}'4'
0:00:00:35 - Rule #66: '} } } } } '5' accepted as '}}}}}'5'
0:00:00:35 - Rule #67: '} } } } } } Y4 '4 d' accepted as '}}}}}}Y4'4d'
0:00:00:36 - Rule #68: '*04 +0 '4' accepted as '*04+0'4'
0:00:00:37 - Rule #69: '*05 O03 d '3 p1' accepted as '*05O03d'3p1'
0:00:00:37 - Rule #70: '+0 +0 +0 +0 +0 +0 +0 +0' accepted as '+0+0+0+0+0+0+0+0'
0:00:00:38 - Rule #71: '+0 +0 +0 O12' accepted as '+0+0+0O12'
0:00:00:38 - Rule #72: 'Z4 '8 O42' accepted as 'Z4'8O42'
0:00:00:39 - Rule #73: 'Z5 '6 O31 ] p1' accepted as 'Z5'6O31]p1'
0:00:00:39 - Rule #74: 'Z5 *75 '5 { O02' accepted as 'Z5*75'5{O02'
0:00:00:40 - Rule #75: 'd O28 Y4 '4 d' accepted as 'dO28Y4'4d'
0:00:00:41 - Rule #76: 'f *A5 '8 O14' accepted as 'f*A5'8O14'
0:00:00:41 - Rule #77: 'p2 '7 p1 O58' accepted as 'p2'7p1O58'
0:00:00:41 - Rule #78: 'O14 d p2 '6' accepted as 'O14dp2'6'
0:00:00:42 Session completed
0:00:00:00 Starting a new session
0:00:00:00 Loaded a total of 1 password hash
0:00:00:00 Cost 1 (MS Office version) is 2013 for all loaded hashes
0:00:00:00 Cost 2 (iteration count) is 100000 for all loaded hashes
0:00:00:00 Command line: /run/john --wordlist=exercise_passwords.txt office_hash.txt 
0:00:00:00 - UTF-8 input encoding enabled
0:00:00:00 - Passwords will be stored UTF-8 encoded in .pot file
0:00:00:00 - Hash type: Office, 2007/2010/2013 (min-len 0, max-len 41 [worst case UTF-8] to 125 [ASCII])
0:00:00:00 - Algorithm: SHA1 256/256 AVX2 8x / SHA512 256/256 AVX2 4x AES
0:00:00:00 - Will reject candidates longer than 125 bytes
0:00:00:00 - Candidate passwords will be buffered and tried in chunks of 160
0:00:00:00 Proceeding with wordlist mode
0:00:00:00 - Wordlist file: exercise_passwords.txt
0:00:00:00 - memory mapping wordlist (5611 bytes)
0:00:00:00 - No word mangling rules
0:00:00:00 - No stacked rules
0:00:00:01 Session completed
