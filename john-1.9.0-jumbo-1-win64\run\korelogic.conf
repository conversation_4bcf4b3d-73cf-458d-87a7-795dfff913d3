####################################################################
# KoreLogic Custom John the Ripper Rules:
####################################################################

# Use this rule with 2EVERYTHING.dic or 3EVERYTHING.dic
[List.Rules:PrependSeason]
a6 A0"[Ss$][uU][mM][mM][eE3][rR]"
a6 A0"[Ww][iI|][nN][tT+][eE3][rR]"
a4 A0"[Ff][aA][lL][lL]"
a6 A0"[Ss][pP][rR][iI][nN][gG]"
a6 A0"[Aa][uU][tT][uU][mM][nN]"

# Use this rule with 2EVERYTHING.dic or 3EVERYTHING.dic
[List.Rules:AppendSeason]
a6 Az"[Ss$][uU][mM][mM][eE3][rR]"
a6 Az"[Ww][iI|][nN][tT+][eE3][rR]"
a6 Az"[Ff][aA][lL][lL]"
a6 Az"[Ss][pP][rR][iI][nN][gG]"
a6 Az"[Aa][uU][tT][uU][mM][nN]"

[List.Rules:PrependHello]
a5 A0"[hH][eE][lL][lL][oO0]"

[List.Rules:PrependYears]
a4 A0"20[0-1][0-9]"
a4 A0"19[3-9][0-9]"

# Notice: Your wordlist should likely be all lowercase - or you are wasting work
[List.Rules:AppendYears]
-[c:] a4 \p[c:] Az"19[0-9][0-9]"
-[c:] a4 \p[c:] Az"20[01][0-9]"

# Notice how we
# 1) do caps first b/c they are more common in 'complex' environments
# 2) Do !$@#%. first b/c they are the most common special chars
[List.Rules:AppendCurrentYearSpecial]
-[c:] a5 \p[c:] Az"201[0-9][!$@#%.]"
-[c:] a5 \p[c:] Azq201[0-9][^&()_+\-={}|[\]\\;'":,/<>?`~*]q

[List.Rules:Append4Num]
-[c:] a4 \p[c:] Az"[0-9][0-9][0-9][0-9]"

[List.Rules:Append5Num]
-[c:] a5 \p[c:] Az"[0-9][0-9][0-9][0-9][0-9]"

[List.Rules:Append6Num]
-[c:] a6 \p[c:] Az"[0-9][0-9][0-9][0-9][0-9][0-9]"

[List.Rules:AppendSpecial3num]
-[c:] a4 \p[c:] Az"[!$@#%.][0-9][0-9][0-9]"
-[c:] a4 \p[c:] Azq[^&()_+\-={}|[\]\\;'":,/<>?`~*][0-9][0-9][0-9]q

[List.Rules:AppendSpecial4num]
-[c:] a5 \p[c:] Az"[!$@#%.][0-9][0-9][0-9][0-9]"
-[c:] a5 \p[c:] Azq[^&()_+\-={}|[\]\\;'":,/<>?`~*][0-9][0-9][0-9][0-9]q

[List.Rules:PrependCAPCAPAppendSpecial]
a3 A0"[A-Z][A-Z]" $[!$@#%.]
a3 A0"[A-Z][A-Z]" $[^&()_+\-={}|[\]\\;'":,/<>?`~*]

[List.Rules:PrependNumNumAppendSpecial]
-[c:] a3 \p[c:] A0"[0-9][0-9]" $[!$@#%.]
-[c:] a3 \p[c:] A0"[0-9][0-9]" $[^&()_+\-={}|[\]\\;'":,/<>?`~*]

[List.Rules:PrependNumNum]
-[c:] a2 \p[c:] A0"[0-9][0-9]"

[List.Rules:PrependNumNumNum]
-[c:] a3 \p[c:] A0"[0-9][0-9][0-9]"

[List.Rules:PrependNumNumNumNum]
-[c:] a4 \p[c:] A0"[0-9][0-9][0-9][0-9]"

[List.Rules:PrependNumNumSpecial]
-[c:] a3 \p[c:] A0"[0-9][0-9][!$@#%.]"
-[c:] a3 \p[c:] A0q[0-9][0-9][^&()_+\-={}|[\]\\;'":,/<>?`~*]q

[List.Rules:Prepend2NumbersAppend2Numbers]
-[c:] a4 \p[c:] A0"[0-9][0-9]" Az"[0-9][0-9]"

[List.Rules:PrependSpecialSpecial]
-[c:] a2 \p[c:] A0q[!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*]q

[List.Rules:AppendSpecialNumberNumber]
-[c:] a3 \p[c:] Az"[!$@#%.][0-9][0-9]"
-[c:] a3 \p[c:] Azq[^&()_+\-={}|[\]\\;'":,/<>?`~*][0-9][0-9]q

[List.Rules:AppendSpecialNumberNumberNumber]
-[c:] a4 \p[c:] Az"[!$@#%.][0-9][0-9][0-9]"
-[c:] a4 \p[c:] Azq[^&()_+\-={}|[\]\\;'":,/<>?`~*][0-9][0-9][0-9]q

[List.Rules:PrependSpecialSpecialAppendNumber]
-[c:] a3 \p[c:] A0"[!$@#%.][!$@#%.]" $[0-9]
-[c:] a3 \p[c:] A0q[^&()_+\-={}|[\]\\;'":,/<>?`~*][^&()_+\-={}|[\]\\;'":,/<>?`~*]q $[0-9]

[List.Rules:PrependSpecialSpecialAppendNumbersNumber]
-[c:] a4 \p[c:] A0"[!$@#%.][!$@#%.]" Az"[0-9][0-9]"
-[c:] a4 \p[c:] A0q[^&()_+\-={}|[\]\\;'":,/<>?`~*][^&()_+\-={}|[\]\\;'":,/<>?`~*]q Az"[0-9][0-9]"

[List.Rules:PrependSpecialSpecialAppendNumbersNumberNumber]
-[c:] a5 \p[c:] A0"[!$@#%.][!$@#%.]" Az"[0-9][0-9][0-9]"
-[c:] a5 \p[c:] A0q[^&()_+\-={}|[\]\\;'":,/<>?`~*][^&()_+\-={}|[\]\\;'":,/<>?`~*]q Az"[0-9][0-9][0-9]"

[List.Rules:Append2Letters]
a2 Az"[a-z][a-z]"
-c a2 Az"[A-Z][A-Z]"
-c a2 Az"[a-z][A-Z]"
-c a2 Az"[A-Z][a-z]"

[List.Rules:Prepend4NumAppendSpecial]
-[c:] a5 \p[c:] A0"[0-9][0-9][0-9][0-9]" $[!$@#%.]
-[c:] a5 \p[c:] A0"[0-9][0-9][0-9][0-9]" Azq[^&()_+\-={}|[\]\\;'":,/<>?`~*]q

[List.Rules:Append4NumSpecial]
-[c:] a5 \p[c:] Az"[0-9][0-9][0-9][0-9][!$@#%.]"
-[c:] a5 \p[c:] Azq[0-9][0-9][0-9][0-9][^&()_+\-={}|[\]\\;'":,/<>?`~*]q

[List.Rules:Append3NumSpecial]
-[c:] a4 \p[c:] Az"[0-9][0-9][0-9][!$@#%.]"
-[c:] a4 \p[c:] Azq[0-9][0-9][0-9][^&()_+\-={}|[\]\\;'":,/<>?`~*]q

[List.Rules:Append2NumSpecial]
-[c:] a3 \p[c:] Az"[0-9][0-9][!$@#%.]"
-[c:] a3 \p[c:] Azq[0-9][0-9][^&()_+\-={}|[\]\\;'":,/<>?`~*]q

# Append numbers - but limit the total length.
[List.Rules:AddJustNumbers]
-[c:] <* >1 \p[c:] $[0-9]
-[c:] <* >1 \p[c:] ^[0-9]
-[c:] <- >1 \p[c:] Az"[0-9][0-9]"
-[c:] <- >1 \p[c:] A0"[0-9][0-9]"
-[c:] a3 >1 \p[c:] Az"[0-9][0-9][0-9]"
-[c:] a4 >1 \p[c:] Az"[0-9][0-9][0-9][0-9]"

[List.Rules:DevProdTestUAT]
-\r[::cc] a3 A\p\r[0l0l]"dev" \p\r[::TT]\p\r[::0l]
-\r[::cc] a3 A\p\r[0l0l]"uat" \p\r[::TT]\p\r[::0l]
-\r[::cc] a4 A\p\r[0l0l]"prod" \p\r[::TT]\p\r[::0l]
-\r[::cc] a4 A\p\r[0l0l]"test" \p\r[::TT]\p\r[::0l]

[List.Rules:PrependAndAppendSpecial]
-[c:] a2 \p[c:] ^[!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*] $[!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*]

[List.Rules:AppendJustNumbers]
-[c:] <* \p[c:] $[0-9]
-[c:] <- \p[c:] Az"[0-9][0-9]"
-[c:] a3 \p[c:] Az"[0-9][0-9][0-9]"
-[c:] a4 \p[c:] Az"[0-9][0-9][0-9][0-9]"

[List.Rules:AppendNumbers_and_Specials_Simple]
# cap first letter then add a 0  2 6 9 ! *  to the end
-[c:] a1 \p[c:] $[0-9]
-[c:] a1 \p[c:] $[!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*]
# cap first letter then add a special char - THEN a number  !0 %9 !9 etc
-[c:] a2 \p[c:] Azq[!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*][0-9]q
# Cap the first letter - then add 0? 0! 5_ .. 9!
## add NUMBER then SPECIAL    1! .. 9?
-[c:] a2 \p[c:] Azq[0-9][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*]q
## Add Number Number Special
-[c:] a3 \p[c:] Azq[0-9][0-9][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*]q
## Add Special Number Number
-[c:] a3 \p[c:] Azq[!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*][0-9][0-9]q
# Add 100! ... 999! to the end
-[c:] a4 \p[c:] Azq[0-9][0-9][0-9][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*]q

[List.Rules:AppendJustSpecials]
-[c:] a1 \p[c:] $[!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*]
-[c:] a2 \p[c:] Azq[!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*]q

[List.Rules:MonthsFullPreface]
-[:c] a7 A0"\p[jJ]anuary"
-[:c] a8 A0"\p[fF]ebruary"
-[:c] a5 A0"\p[mM]arch"
-[:c] a5 A0"\p[aA]pril"
-[:c] a3 A0"\p[mM]ay"
-[:c] a4 A0"\p[jJ]une"
-[:c] a4 A0"\p[jJ]uly"
-[:c] a6 A0"\p[aA]ugust"
-[:c] a9 A0"\p[sS]eptember"
-[:c] a7 A0"\p[oO]ctober"
-[:c] a8 A0"\p[nN]ovember"
-[:c] a8 A0"\p[dD]ecember"

[List.Rules:AddShortMonthsEverywhere]
a3 >\r[00123456789] A\p[z0-9]"[jJ][aA][nN]"
a3 >\r[00123456789] A\p[z0-9]"[fF][eE][bB]"
a3 >\r[00123456789] A\p[z0-9]"[mM][aA][rRyY]"
a3 >\r[00123456789] A\p[z0-9]"[aA][pP][rR]"
a3 >\r[00123456789] A\p[z0-9]"[jJ][uU][nNlL]"
a3 >\r[00123456789] A\p[z0-9]"[aA][uU][gG]"
a3 >\r[00123456789] A\p[z0-9]"[sS][eE][pP]"
a3 >\r[00123456789] A\p[z0-9]"[oO][cC][tT]"
a3 >\r[00123456789] A\p[z0-9]"[nN][oO][vV]"
a3 >\r[00123456789] A\p[z0-9]"[dD][eE][cC]"

[List.Rules:Prepend4LetterMonths]
## Preface each dictionary with Janu janu Febr febr
-[:c] a4 A0"\p[jJ]anu"
-[:c] a4 A0"\p[fF]ebr"
-[:c] a4 A0"\p[mM]arc"
-[:c] a3 A0"\p[aA]pr"
-[:c] a3 A0"\p[mM]ay"
-[:c] a4 A0"\p[jJ]une"
-[:c] a4 A0"\p[jJ]uly"
-[:c] a4 A0"\p[Aa]ugu"
-[:c] a4 A0"\p[sS]ept"
-[:c] a4 A0"\p[oO]cto"
-[:c] a4 A0"\p[nN]ove"
-[:c] a4 A0"\p[Dd]ece"

# this will add the string '2010' at all places in the word:
# USE this with a 4 or 5 char dictionary file with ALL characters
# soo abcde will become
# 2010abcde  a2010bcde  ab2010cde  acd2010de  abcd2010e  abcde2010
[List.Rules:Add2010Everywhere]
a4 >\r[00123456789] A\p[z0-9]"201[0-9]"

[List.Rules:PrependDaysWeek]
a6 A0"[Mm][oO0][nN][dD][aA4@][yY]"
a7 A0"[Tt][uU][eE3][sS$][dD][aA4@][yY]"
a9 A0"[Ww][eE3][dD][nN][eE3][sS$][dD][aA4@][yY]"
a8 A0"[Tt][hH][uU][rR][sS$][dD][aA4@][yY]"
a6 A0"[Ff][rR][iI1!][dD][aA4@][yY]"
a8 A0"[Ss][aA4@][tT+][uU][rR][dD][aA4@][yY]"
a6 A0"[Ss][uU][nN][dD][aA4@][yY]"

[List.Rules:Add1234_Everywhere]
a4 >\r[00123456789] A\p[z0-9]"1234"

[List.Rules:AppendMonthDay]
-[:c] <* Az"\p[jJ]anuary"
-[:c] a8 Az"\p[jJ]anuary[0-9]"
-[:c] a9 Az"\p[jJ]anuary[0-9][0-9]"
-[:c] <* Az"\p[fF]ebruary"
-[:c] a9 Az"\p[fF]ebruary[0-9]"
-[:c] aA Az"\p[fF]ebruary[0-9][0-9]"
-[:c] <* Az"\p[mM]arch"
-[:c] a6 Az"\p[mM]arch[0-9]"
-[:c] a7 Az"\p[mM]arch[0-9][0-9]"
-[:c] <* Az"\p[aA]pril"
-[:c] a6 Az"\p[aA]pril[0-9]"
-[:c] a7 Az"\p[aA]pril[0-9][0-9]"
-[:c] <* Az"\p[mM]ay"
-[:c] a4 Az"\p[mM]ay[0-9]"
-[:c] a5 Az"\p[mM]ay[0-9][0-9]"
-[:c] <* Az"\p[jJ]une"
-[:c] a5 Az"\p[jJ]une[0-9]"
# There was a typo in Kore's original revision of this rule
-[:c] a6 Az"\p[jJ]une[0-9][0-9]"
-[:c] <* Az"\p[jJ]uly"
-[:c] a5 Az"\p[jJ]uly[0-9]"
-[:c] a6 Az"\p[jJ]uly[0-9][0-9]"
-[:c] <* Az"\p[aA]ugust"
-[:c] Az"\p[aA]ugust[0-9]"
-[:c] Az"\p[aA]ugust[0-9][0-9]"
-[:c] <* Az"\p[sS]eptember"
-[:c] aA Az"\p[sS]eptember[0-9]"
# There was a typo in Kore's original revision of this rule
-[:c] aB Az"\p[sS]eptember[0-9][0-9]"
-[:c] <* Az"\p[oO]ctober"
-[:c] a8 Az"\p[oO]ctober[0-9]"
-[:c] a9 Az"\p[oO]ctober[0-9][0-9]"
-[:c] <* Az"\p[nN]ovember"
-[:c] a9 Az"\p[nN]ovember[0-9]"
-[:c] aA Az"\p[nN]ovember[0-9][0-9]"
-[:c] <* Az"\p[dD]ecember"
-[:c] a9 Az"\p[dD]ecember[0-9]"
-[:c] aA Az"\p[dD]ecember[0-9][0-9]"

[List.Rules:AppendMonthCurrentYear]
-[:c] a7 Az"\p[jJ]an201[0-9]"
-[:c] a7 Az"\p[fF]eb201[0-9]"
-[:c] a7 Az"\p[mM]ar201[0-9]"
-[:c] a7 Az"\p[aA]pr201[0-9]"
-[:c] a7 Az"\p[mM]ay201[0-9]"
-[:c] a7 Az"\p[jJ]un201[0-9]"
-[:c] a7 Az"\p[jJ]ul201[0-9]"
-[:c] a7 Az"\p[Aa]ug201[0-9]"
-[:c] a7 Az"\p[sS]ep201[0-9]"
-[:c] a7 Az"\p[oO]ct201[0-9]"
-[:c] a7 Az"\p[nN]ov201[0-9]"
-[:c] a7 Az"\p[Dd]ec201[0-9]"

[List.Rules:ReplaceNumbers2Special]
a0 /[1-90] s\0\p[!@#$%^&*()]
a0 /1 /[2-90] s1! s\0\p[@#$%^&*()]
a0 /2 /[3-90] s2@ s\0\p[#$%^&*()]
a0 /3 /[4-90] s3# s\0\p[$%^&*()]
a0 /4 /[5-90] s4$ s\0\p[%^&*()]
a0 /5 /[6-90] s5% s\0\p[^&*()]
a0 /6 /[7-90] s6^ s\0\p[&*()]
a0 /7 /[890] s7& s\0\p[*()]
a0 /8 /[90] s8* s\0\p[()]
a0 /9 /0 s9( s0)

[List.Rules:ReplaceNumbers]
a0 /0 s0[1-9]
a0 /1 s1[02-9]
a0 /2 s2[013-9]
a0 /3 s3[0-24-9]
a0 /4 s4[0-35-9]
a0 /5 s5[0-46-9]
a0 /6 s6[0-57-9]
a0 /7 s7[0-68-9]
a0 /8 s8[0-79]
a0 /9 s9[0-8]
# 10 lines above can be replaced with just one:
# /[0-9] s\0[0-9] Q
# but it's slower (generates, then rejects some duplicates).

# This is a lamer/faster version of --rules:nt
[List.Rules:ReplaceLettersCaps]
-c a0 /[a-z] s\0\p[A-Z]

[List.Rules:AddDotCom]
-[c:] a4 \p[c:] Az".com"
-[c:] a4 \p[c:] Az".net"
-[c:] a4 \p[c:] Az".org"

[List.Rules:AppendCap-Num_or_Special-Twice]
-[c:] a3 \p[c:] Az"[A-Z][0-9][0-9]"
-[c:] a3 \p[c:] Azq[A-Z][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*][0-9]q
-[c:] a3 \p[c:] Azq[A-Z][0-9][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*]q
-[c:] a3 \p[c:] Azq[A-Z][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*]q

[List.Rules:AppendSpecialLowerLower]
-[c:] a3 \p[c:] AzQ[!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*][a-z][a-z]Q

[List.Rules:AppendJustSpecials3Times]
-[c:] a3 \p[c:] Az"[!$@#%.][!$@#%.][!$@#%.]"
-[c:] a3 \p[c:] Azq[!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*]q

[List.Rules:PrependJustSpecials]
-[c:] a1 \p[c:] ^[!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*]
-[c:] a2 \p[c:] A0q[!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*]q

[List.Rules:Append1_AddSpecialEverywhere]
-[c:] >4 a2 \p[c:] i[0-5][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*] $1
-[c:] >[5-8] a2 \p1[c:] i\p2[6-9][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*] $1

[List.Rules:PrependNumNum_AppendNumSpecial]
-[c:] a4 \p[c:] A0"[0-9][0-9]" Azq[0-9][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*]q

[List.Rules:AppendNum_AddSpecialEverywhere]
# This should probably use $[02-9] since we try $1 in
# Append1_AddSpecialEverywhere
-[c:] >4 a2 \p[c:] i[0-5][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*] $[02-9]
-[c:] >[5-8] a2 \p1[c:] i\p2[6-9][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*] $[02-9]

[List.Rules:AppendNumNum_AddSpecialEverywhere]
-[c:] >4 a3 \p[c:] i[0-5][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*] Az"[0-9][0-9]"
-[c:] >[5-8] a3 \p1[c:] i\p2[6-9][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*] Az"[0-9][0-9]"

[List.Rules:AppendNumNumNum_AddSpecialEverywhere]
-[c:] >4 a4 \p[c:] i[0-5][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*] Az"[0-9][0-9][0-9]"
-[c:] >[5-8] a4 \p1[c:] i\p2[6-9][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*] Az"[0-9][0-9][0-9]"

[List.Rules:AppendYears_AddSpecialEverywhere]
-[c:] >4 a5 \p[c:] i[0-5][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*] Az"19[4-9][0-9]"
-[c:] >4 a5 \p[c:] i[0-5][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*] Az"20[0-1][0-9]"
-[c:] >[5-8] a5 \p1[c:] i\p2[6-9][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*] Az"19[4-9][0-9]"
-[c:] >[5-8] a5 \p1[c:] i\p2[6-9][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*] Az"20[0-1][0-9]"

# This rule needs work actually --- you have to 'sort -u' its output    rick
# /a = reject if it doesnt have an 'a'
# the [:c] does waste some effort - and generate dupes. This is wasteful,
# but I want to keep it in b/c the original crack/JtR rules use it.
[List.Rules:L33t]
-[:c] a0 /\r[aaAAbBeEiiiIIIllll] s\0\r\p[@44@88331!|1!|17|!] \p1[:M] \p1[:c] \p1[:Q]
# The following line differs from Kore's erroneous 4 lines:
-[:c] a0 /\r[LLLL] s\0\r\p[17|!] \p1[:M] \p1[:c] \p1[:Q]
#/Lsl1[:c]
#/Lsl7[:c]
#/Lsl|[:c]
#/Lsl![:c]
-[:c] a0 /\r[oOssSStT1111003344557788] s\0\r\p[00$5$5++!iI|oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
# Full set (same as above, but on one line):
#-[:c] /\r[aaAAbBeEiiiIIIllllLLLLoOssSStT1111003344557788] s\0\r\p[@44@88331!|1!|17|!17|!00$5$5++!iI|oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
# Double substitutions start here.
# Compared to Kore's, we check for both chars first, then replace both.
# This produces different results from Kore's, which would replace all
# instances of the first char before checking for the second.
# Kore's behavior may be restored by moving "sa[@4]" to be right after "/a"
# on the line below, and ditto for further lines.
-[:c] a0 /a /\r[AAbBeEiiiIIIllllLLLLoOssSStT111100334@557788] sa[@4] s\2\r\p2[4@88331!|1!|17|!17|!00$5$5++!iI|oOeE@4sSlLbB] \p1[:M] \p1[:c] \p1[:Q]
# Kore had these (probably unintentionally, so we don't duplicate them):
#/asa4/4s4a[:c]
#/asa4/4s4A[:c]
-[:c] a0 /A /\r[aabBeEiiiIIIllllLLLLoOssSStT1111003344557788] sA4 s\0\r\p[@488331!|1!|17|!17|!00$5$5++!iI|oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
# Kore also had these, but (intentionally?) missed sb8 on this set (after sA4)
#/AsA4/4s4a[:c]
#/AsA4/4s4A[:c]
-[:c] a0 /b /\r[aaAABeEiiiIIIllllLLLLoOssSStT1111003344557788] sb8 s\0\r\p[@44@8331!|1!|17|!17|!00$5$5++!iI|oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /B /\r[aaAAbeEiiiIIIllllLLLLoOssSStT1111003344557788] sB8 s\0\r\p[@44@8331!|1!|17|!17|!00$5$5++!iI|oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /e /\r[aaAAbBEiiiIIIllllLLLLoOssSStT1111003344557788] se3 s\0\r\p[@44@8831!|1!|17|!17|!00$5$5++!iI|oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /E /\r[aaAAbBeiiiIIIllllLLLLoOssSStT1111003344557788] sE3 s\0\r\p[@44@8831!|1!|17|!17|!00$5$5++!iI|oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /i /\r[aaAAbBeEIIIllllLLLLoOssSStT1111003344557788] si[1!|] s\2\r\p2[@44@88331!|17|!17|!00$5$5++!iI|oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /I /\r[aaAAbBeEiiillllLLLLoOssSStT1111003344557788] sI[1!|] s\2\r\p2[@44@88331!|17|!17|!00$5$5++!iI|oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
# Kore's rules only included sl[17|], but not sl!
-[:c] a0 /l /\r[aaAAbBeEiiiIIILLLLoOssSStT1111003344557788] sl[17|!] s\2\r\p2[@44@88331|17|!17|!00$5$5++!iI|oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
# All "/L" rules (171 lines) were buggy
-[:c] a0 /L /\r[aaAAbBeEiiiIIIlllloOssSStT1111003344557788] sl[17|!] s\2\r\p2[@44@88331|17|!17|!00$5$5++!iI|oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /o /\r[aaAAbBeEiiiIIIllllLLLLOssSStT1111003344557788] so0 s\0\r\p[@44@88331!|1!|17|!17|!0$5$5++!iI|oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /O /\r[aaAAbBeEiiiIIIllllLLLLossSStT1111003344557788] sO0 s\0\r\p[@44@88331!|1!|17|!17|!0$5$5++!iI|oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /s /\r[aaAAbBeEiiiIIIllllLLLLoOSStT1111003344557788] ss[$5] s\2\r\p2[@44@88331!|1!|17|!17|!00$5++!iI|oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /S /\r[aaAAbBeEiiiIIIllllLLLLoOsstT1111003344557788] sS[$5] s\2\r\p2[@44@88331!|1!|17|!17|!00$5++!iI|oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /t /\r[aaAAbBeEiiiIIIllllLLLLoOssSST1111003344557788] st+ s\0\r\p[@44@88331!|1!|17|!17|!00$5$5+!iI|oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /T /\r[aaAAbBeEiiiIIIllllLLLLoOssSSt1111003344557788] sT+ s\0\r\p[@44@88331!|1!|17|!17|!00$5$5+!iI|oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
# are these 100% redundant from above rules? !!!!
-[:c] a0 /1 /\r[aaAAbBeEiiiIIIllllLLLLoOssSStT003344557788] s1[!iI|] s\2\r\p2[@44@88331!|1!|17|!17|!00$5$5++oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /0 /\r[aaAAbBeEiiiIIIllllLLLLoOssSStT11113344557788] s0[oO] s\2\r\p2[@44@88331!|1!|17|!17|!00$5$5++!iI|eEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /3 /\r[aaAAbBeEiiiIIIllllLLLLoOssSStT11110044557788] s3[eE] s\2\r\p2[@44@88331!|1!|17|!17|!00$5$5++!iI|oOaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
#-[:c] /\r[aaAAbBeEiiiIIIllllLLLLoOssSStT1111003344557788] s\0\r\p[@44@88331!|1!|17|!17|!00$5$5++!iI|oOeEaAsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /4 /\r[aaAAbBeEiiiIIIllllLLLLoOssSStT11110033557788] s4[aA] s\2\r\p2[@44@88331!|1!|17|!17|!00$5$5++!iI|oOeEsSlLbB] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /5 /\r[aaAAbBeEiiiIIIllllLLLLoOssSStT11110033447788] s5[sS] s\2\r\p2[@44@88331!|1!|17|!17|!00$5$5++!iI|oOeEaAlLbB] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /7 /\r[aaAAbBeEiiiIIIllllLLLLoOssSStT11110033445588] s7[lL] s\2\r\p2[@44@88331!|1!|17|!17|!00$5$5++!iI|oOeEaAsSbB] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /8 /\r[aaAAbBeEiiiIIIllllLLLLoOssSStT11110033445577] s8[bB] s\2\r\p2[@44@88331!|1!|17|!17|!00$5$5++!iI|oOeEaAsSlL] \p1[:M] \p1[:c] \p1[:Q]
# These are some popular triple/quad l33t rules
-[:c] a0 /a /e /[los] sa4 se3 s\0\p[10$] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /[ae] /l /[os] s\2\p2[43] sl1 s\3\p3[0$] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /[ae] /o /s s\2\p2[43] so0 ss$ \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /l /o /s sl1 so0 ss$ \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /a /e /l /[os] sa4 se3 sl1 s\0\p[0$] \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /a /[el] /o /s sa4 s\0\p[31] so0 ss$ \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /e /l /o /s se3 sl1 so0 ss$ \p1[:M] \p1[:c] \p1[:Q]
-[:c] a0 /a /e /l /o /s sa4 se3 sl1 so0 ss$ \p1[:M] \p1[:c] \p1[:Q]

[List.Rules:ReplaceSpecial2Special]
# Kore's rules were missing "*"
# Kore's rules were missing ?[]{}`~
# Now converted into just a SINGLE rule (well 2 since ? must use class syntax)
#    The rules do add a Q to avoid no-op, but it is now 2 'working' rules
#    NOTE, there were numerous rules which also had problems, which were fixed
#    (in commented out rules), and are 'right' in the 2 new replacement rules.
# Now thru some pre-processor jiu jitsu, this was reduced to a single rule line
a0 /[!@#$%^&*()\-=_+\\|;:'",./><\[\]{}`~?]\p\r[:::::::::::::::::::::::::::::::?] \p\r[:::::::::::::::::::::::::::::::s]\p\r[sssssssssssssssssssssssssssssss?]\1[!@#$%^&*()\-=_+\\|;:'",./?><\[\]{}`~] Q
#these 2 are replaced by the equivalent above 1 rule.
# /[!@#$%^&*()\-=_+\\|;:'",./><\[\]{}`~] s\0[!@#$%^&*()\-=_+\\|;:'",./?><\[\]{}`~] Q
# /?? s??[!@#$%^&*()\-=_+\\|;:'",./><\[\]{}`~]
#these are replaced by the equivalent above 2 rule lines.
# /! s![@#$%^&*()\-=_+\\|;:'",./?><\[\]{}`~]
# /@ s@[!#$%^&*()\-=_+\\|;:'",./?><\[\]{}`~]
#others replacing #$%^&*()-=_+\|;:'",./?><[]{}`~ cut out, and not shown.

[List.Rules:ReplaceLetters]
a0 /[a-z] s\0[a-z] Q
-c a0 /[a-z] s\0[A-Z]

####################################################################
# This ruleset contains ALL of the above, for a total
# of 7,074,074 rules after dupe removal
[List.Rules:KoreLogic]
.include [List.Rules:PrependNumNum]
.include [List.Rules:PrependYears]
.include [List.Rules:AppendYears]
.include [List.Rules:PrependNumNumNum]
.include [List.Rules:MonthsFullPreface]
.include [List.Rules:Prepend4LetterMonths]
.include [List.Rules:PrependSeason]
.include [List.Rules:AppendSeason]
.include [List.Rules:PrependHello]
.include [List.Rules:AppendCurrentYearSpecial]
.include [List.Rules:PrependSpecialSpecial]
.include [List.Rules:Append2Letters]
.include [List.Rules:AddJustNumbers]
.include [List.Rules:DevProdTestUAT]
.include [List.Rules:PrependAndAppendSpecial]
.include [List.Rules:AppendJustNumbers]
# This is split for better order:
# First part of AppendNumbers_and_Specials_Simple
-[c:] a1 \p[c:] $[0-9]
-[c:] a1 \p[c:] $[!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*]
-[c:] a2 \p[c:] Azq[!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*][0-9]q
-[c:] a2 \p[c:] Azq[0-9][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*]q
.include [List.Rules:AppendJustSpecials]
.include [List.Rules:AddShortMonthsEverywhere]
.include [List.Rules:Add2010Everywhere]
.include [List.Rules:Add1234_Everywhere]
.include [List.Rules:AppendMonthDay]
.include [List.Rules:AppendMonthCurrentYear]
.include [List.Rules:ReplaceNumbers2Special]
.include [List.Rules:ReplaceNumbers]
.include [List.Rules:ReplaceLettersCaps]
.include [List.Rules:AddDotCom]
.include [List.Rules:PrependJustSpecials]
.include [List.Rules:Append1_AddSpecialEverywhere]
.include [List.Rules:AppendNum_AddSpecialEverywhere]
.include [List.Rules:AppendNumNum_AddSpecialEverywhere]
.include [List.Rules:AppendNumNumNum_AddSpecialEverywhere]
.include [List.Rules:AppendYears_AddSpecialEverywhere]
.include [List.Rules:L33t]
.include [List.Rules:ReplaceSpecial2Special]
.include [List.Rules:ReplaceLetters]
.include [List.Rules:AppendSpecialNumberNumber]
.include [List.Rules:PrependNumNumAppendSpecial]
.include [List.Rules:PrependNumNumSpecial]
.include [List.Rules:Append2NumSpecial]
.include [List.Rules:PrependDaysWeek]
# Second part of AppendNumbers_and_Specials_Simple
-[c:] a3 \p[c:] Azq[0-9][0-9][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*]q
-[c:] a3 \p[c:] Azq[!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*][0-9][0-9]q
.include [List.Rules:PrependSpecialSpecialAppendNumber]
.include [List.Rules:Append4Num]
.include [List.Rules:PrependNumNumNumNum]
.include [List.Rules:Prepend2NumbersAppend2Numbers]
.include [List.Rules:PrependCAPCAPAppendSpecial]
.include [List.Rules:AppendSpecialLowerLower]
# Last part of AppendNumbers_and_Specials_Simple
-[c:] a4 \p[c:] Azq[0-9][0-9][0-9][!$@#%.^&()_+\-={}|[\]\\;'":,/<>?`~*]q
.include [List.Rules:AppendSpecial3num]
.include [List.Rules:AppendSpecialNumberNumberNumber]
.include [List.Rules:Append3NumSpecial]
.include [List.Rules:PrependNumNum_AppendNumSpecial]
.include [List.Rules:AppendJustSpecials3Times]
.include [List.Rules:AppendCap-Num_or_Special-Twice]
.include [List.Rules:PrependSpecialSpecialAppendNumbersNumber]
.include [List.Rules:Append5Num]
.include [List.Rules:AppendSpecial4num]
.include [List.Rules:Prepend4NumAppendSpecial]
.include [List.Rules:Append4NumSpecial]
.include [List.Rules:PrependSpecialSpecialAppendNumbersNumberNumber]
.include [List.Rules:Append6Num]
