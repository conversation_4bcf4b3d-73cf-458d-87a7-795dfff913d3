	<PERSON> the Ripper's cracking modes.

Mode descriptions here are short and only cover the basic things.
Check other documentation files for information on customizing the
modes.


	Wordlist mode.

This is the simplest cracking mode supported by <PERSON>.  All you need to
do is specify a wordlist (a text file containing one word per line)
and some password files.  You can enable word mangling rules (which
are used to modify or "mangle" words producing other likely
passwords).  If enabled, all of the rules will be applied to every
line in the wordlist file producing multiple candidate passwords from
each source word.

The wordlist should not contain duplicate lines.  <PERSON> does not sort
entries in the wordlist since that would consume a lot of resources
and would prevent you from making <PERSON> try the candidate passwords in
the order that you define (with more likely candidate passwords listed
first).  However, if you don't list your candidate passwords in a
reasonable order, it'd be better if you sort the wordlist
alphabetically: with some hash types, <PERSON> runs a bit faster if each
candidate password it tries only differs from the previous one by a
few characters.  Most wordlists that you may find on the Net are
already sorted anyway.

On the other hand, if your wordlist is sorted alphabetically, you do
not need to bother about some wordlist entries being longer than the
maximum supported password length for the hash type you're cracking.
To give an example, for traditional DES-based crypt(3) hashes only
the first 8 characters of passwords are significant.  This means that
if there are two or more candidate passwords in the wordlist whose
first 8 characters are exactly the same, they're effectively the same
8 character long candidate password which only needs to be tried once.
As long as the wordlist is sorted alphabetically, <PERSON> is smart enough
to handle this special case right.

In fact, it is recommended that you do not truncate candidate
passwords in your wordlist file since the rest of the characters
(beyond the length limit of your target hash type) are likely still
needed and make a difference if you enable word mangling rules.

The recommended way to sort a wordlist for use with default wordlist
rule set is:

	tr A-Z a-z < SOURCE | sort -u > TARGET

See RULES for information on writing your own wordlist rules.


	"Single crack" mode.

This is the mode you should start cracking with.  It will use the
login names, "GECOS" / "Full Name" fields, and users' home directory
names as candidate passwords, also with a large set of mangling rules
applied.  Since the information is only used against passwords for the
accounts it was taken from (and against password hashes which happened
to be assigned the same salt), "single crack" mode is much faster than
wordlist mode.  This permits for the use of a much larger set of word
mangling rules with "single crack", and their use is always enabled
with this mode.  Successfully guessed passwords are also tried against
all loaded password hashes just in case more users have the same
password.  This can be disabled using SingleRetestGuess config option
or --single-retest-guess=BOOL command-line option (which overrides
any john.conf setting).

You can add one or several static words with --single-seed=WORD[,WORD]
in case some word or name is very common in the passwords (eg. for the
legendary Gawker dump, you'd use "--single-seed=Gawker,Gizmodo".  This
will result in combinations of those words and the login/GECOS data and
has proven very effective.  For more words, the --single-wordlist=FILE
option works the same except it reads words from a wordlist file. When
using either option, SingleWordsPairMax will be automatically increased
if needed - this can increase run time (and success) dramatically.

Note that running this mode on many password files simultaneously may
sometimes get more passwords cracked than it would if you ran it on
the individual password files separately.

Single mode now works fine on GPU except it can't use GPU-side mask
generation, so the few very fastest formats can't reach full speed.
For other modes it works fine but may need a lot of memory (you'll get
helpful messages if you need to adjust).  Also, you may need to ensure
there are enough rules and/or base words in use in order to saturate the
GPU buffers.  Examples of doing so are using "--single:all" and/or
enabling john.conf parameters "JumboSingleWords" and "PristineGecos".
Also see the seed options above.


	"Incremental" mode.

This is the most powerful cracking mode, it can try all possible
character combinations as passwords.  However, it is assumed that
cracking with this mode will never terminate because of the number of
combinations being too large (actually, it will terminate if you set a
low password length limit or make it use a small charset), and you'll
have to interrupt it earlier.

That's one reason why this mode deals with trigraph frequencies,
separately for each character position and for each password length,
to crack as many passwords as possible within a limited time.

To use the mode you need a specific definition for the mode's
parameters, including password length limits and the charset to use.
These parameters are defined in the configuration file sections called
[Incremental:MODE], where MODE is any name that you assign to the mode
(it's the name that you will need to specify on John's command line).
You can either use a pre-defined incremental mode definition or define a
custom one.

As of version 1.8.0, pre-defined incremental modes are "ASCII" (all 95
printable ASCII characters), "LM_ASCII" (for use on LM hashes), "Alnum"
(all 62 alphanumeric characters), "Alpha" (all 52 letters), "LowerNum"
(lowercase letters plus digits, for 36 total), "UpperNum" (uppercase
letters plus digits, for 36 total), "LowerSpace" (lowercase letters plus
space, for 27 total), "Lower" (lowercase letters), "Upper" (uppercase
letters), and "Digits" (digits only).  The supplied .chr files include
data for lengths up to 13 for all of these modes except for "LM_ASCII"
(where password portions input to the LM hash halves are assumed to be
truncated at length 7) and "Digits" (where the supplied .chr file and
pre-defined incremental mode work for lengths up to 20).  Some of the
many .chr files needed by these pre-defined incremental modes might not
be bundled with every version of John the Ripper, being available as a
separate download.

See CONFIG and EXAMPLES for information on defining custom modes.


	External mode.

You can define an external cracking mode for use with John.  This is
done with the configuration file sections called [List.External:MODE],
where MODE is any name that you assign to the mode.  The section
should contain program code of some functions that John will use to
generate the candidate passwords it tries.  The functions are coded in
a subset of C and are compiled by John at startup when you request the
particular external mode on John's command line.  See EXTERNAL.

	Markov mode.

This is a stand alone mode, where word-like strings are created based
upon a complex algorithm based upon 'weights' or likeliness of characters
following characters.  A full description of this mode and its usage is
found in the ./doc/MARKOV document.

	MASK mode.

This allows creation of a mask of character classes which can be in
specific positions when building a candidate to test.  There is a full
description of this mode, and the masks available, in the ./doc/MASK
document.

	SUBSETS mode.

This mode exploits the weakness of low entropy passwords and was inspired
by 'subsets' external mode, but has many advantages.  Basically, it will
try subsets of a given charset with increasing lengths. It divides the
keyspace in many sets of (subset size, password length) and will always
try a smaller sub-keyspace before a larger one. For example, it will try
very long passwords consisting of few unique characters before it tries a
short one with more unique ones.  See ./doc/SUBSETS for more details.

	REGEX mode (experimental, not supported, see README.librexgen).

You can think of this as "reverse regexp".  Regex is similar to mask mode but
more powerful (and also slower).  Basically, you write a regular expression
and the regex mode will create all possible candidates that would match it.
Regex can optionally be used in hybrid (a.k.a stacked) mode, using \0 as a
placeholder for the parent mode's word.  Eg. a regex of "\0\d\d" will produce
the same result as a rule adding two digits to the base word.

	Multiple stacking of modes.

The mask and regex modes can be stacked upon some other mode (any mode except
single, but including e.g. stdin and loopback modes).  This is sometimes
referred to as "hybrid mask" or "hybrid regex" modes.  Regex can be a parent
mode to mask mode even when regex itself runs stacked on some other mode.  In
that case, mask is always last in chain  (by design, since it can accelerate
fast GPU formats).  Basically, the first mode is any mode except single, and
then you can optionally add rules (provided the base mode supports it),  then
optionally regex, then optionally mask and finally an optional external filter.

	What modes should I use?

See EXAMPLES for a reasonable order of cracking modes to use.  When choosing
between rules, regex or mask as an "amplifier", you can often pick any of them
with the same result.  For example, a rule of 'Az"[0-9][0-9]"', or a hybrid
regex of '\0\d\d', or a hybrid mask of '?w?d?d' - all will produce the exact
same results (except perhaps in a different order) but an important difference
is that mask mode is the fastest alternative (and rules are the slowest), and
in case of using a GPU-enabled format that supports it, mask mode will even be
accelerated on GPU with a huge performance boost.  Having said that, there are
things you can only do with rules, and other things that only regex can
achieve, so mask mode is not always the best fit.

$Owl$
