#!/usr/bin/env python3
"""
使用新发现的密码候选尝试解密
"""

import msoffcrypto
import io
import os

def try_new_passwords():
    """尝试新的密码候选"""
    print("使用新发现的密码候选尝试解密...")
    
    filename = "Final Exam(MS).docx"
    
    # 新的密码候选
    new_passwords = [
        # 数学模式相关
        "31", "63", "95", "127", "159", "191", "223", "255",
        "1f", "3f", "5f", "7f", "9f", "bf", "df", "ff",
        "31639527", "31-63-95-127",
        "11111", "111111", "1111111", "11111111",
        
        # 第一行的变体
        "a-d", "TJO", "TJO2", "a-dTJO", "dTJO2",
        
        # 基于移位的结果
        "b-eUKP", "c-fVLQ", "d-gWMR", "e-hXNS",
        
        # 基于Base64解码的结果
        "zFBM", "rSʗ",
        
        # 组合密码
        "31TJO", "63TJO", "127TJO", "255TJO",
        "TJO31", "TJO63", "TJO127", "TJO255",
        
        # 十六进制组合
        "1f3f7fff", "31637f", "a-d1f",
        
        # 其他可能的组合
        "final31", "exam63", "test127", "password255",
        "31-63", "63-95", "95-127", "127-159",
        
        # 基于XML内容的密码
        "TermInfo", "element", "complexType", "sequence",
        
        # 更多变体
        "a-d31", "a-d63", "a-d127", "a-d255",
        "31a-d", "63a-d", "127a-d", "255a-d",
        
        # 二进制相关
        "10011111", "10111111", "11011111",
        "9f", "bf", "df",
        
        # 可能的简单密码
        "31-63-95", "63-95-127", "95-127-159",
        "1-3-7-15", "3-7-15-31", "7-15-31-63",
        
        # 基于发现的字符串
        "0HpGQvBNss", "eebu4HWcP", "TEtaeDET9"
    ]
    
    print(f"尝试 {len(new_passwords)} 个新密码...")
    
    for i, password in enumerate(new_passwords):
        try:
            print(f"尝试密码 {i+1:2d}: {password}")
            
            with open(filename, "rb") as f:
                office_file = msoffcrypto.OfficeFile(f)
                office_file.load_key(password=password)
                
                decrypted = io.BytesIO()
                office_file.decrypt(decrypted)
                
                # 成功解密
                output_filename = f"decrypted_success_{password.replace('/', '_').replace(':', '_').replace('-', '_')}.docx"
                with open(output_filename, "wb") as out:
                    out.write(decrypted.getvalue())
                
                print(f"✓ 成功解密！密码是: {password}")
                print(f"解密文件保存为: {output_filename}")
                
                return password, output_filename
                
        except Exception as e:
            if "Invalid password" not in str(e) and "could not be decrypted" not in str(e):
                print(f"  错误: {e}")
            continue
    
    print("所有新密码都失败了")
    return None, None

def try_brute_force_short():
    """尝试短密码的暴力破解"""
    print("\n尝试短密码暴力破解...")
    
    filename = "Final Exam(MS).docx"
    
    # 尝试1-4位的数字密码
    print("尝试数字密码...")
    for num in range(1, 10000):
        password = str(num)
        try:
            with open(filename, "rb") as f:
                office_file = msoffcrypto.OfficeFile(f)
                office_file.load_key(password=password)
                
                decrypted = io.BytesIO()
                office_file.decrypt(decrypted)
                
                output_filename = f"decrypted_brute_{password}.docx"
                with open(output_filename, "wb") as out:
                    out.write(decrypted.getvalue())
                
                print(f"✓ 暴力破解成功！密码是: {password}")
                return password, output_filename
                
        except:
            continue
        
        if num % 1000 == 0:
            print(f"  已尝试到 {num}")
    
    print("数字暴力破解失败")
    return None, None

def analyze_decrypted_file(filename):
    """分析解密后的文件"""
    if not filename or not os.path.exists(filename):
        return
    
    print(f"\n分析解密文件: {filename}")
    
    try:
        # 尝试提取文本内容
        import zipfile
        
        with zipfile.ZipFile(filename, 'r') as docx:
            # 列出文件内容
            print("文档内容:")
            for file_info in docx.filelist:
                print(f"  {file_info.filename}")
            
            # 尝试读取主文档内容
            if 'word/document.xml' in docx.namelist():
                with docx.open('word/document.xml') as doc_xml:
                    content = doc_xml.read().decode('utf-8')
                    print(f"\n文档XML内容 (前500字符):")
                    print(content[:500])
                    
                    # 保存完整XML
                    with open("document_content.xml", "w", encoding='utf-8') as f:
                        f.write(content)
                    print("完整XML内容已保存到: document_content.xml")
                    
                    # 尝试提取纯文本
                    import re
                    text_content = re.sub(r'<[^>]+>', '', content)
                    text_content = ' '.join(text_content.split())
                    
                    if text_content.strip():
                        print(f"\n提取的文本内容:")
                        print(text_content)
                        
                        with open("extracted_text.txt", "w", encoding='utf-8') as f:
                            f.write(text_content)
                        print("文本内容已保存到: extracted_text.txt")
                    
    except Exception as e:
        print(f"分析文件失败: {e}")

def main():
    # 尝试新密码
    password, decrypted_file = try_new_passwords()
    
    if password:
        print(f"\n🎉 解密成功！")
        print(f"密码: {password}")
        print(f"解密文件: {decrypted_file}")
        analyze_decrypted_file(decrypted_file)
        return
    
    # 如果新密码失败，尝试暴力破解
    print("\n新密码失败，尝试暴力破解...")
    password, decrypted_file = try_brute_force_short()
    
    if password:
        print(f"\n🎉 暴力破解成功！")
        print(f"密码: {password}")
        print(f"解密文件: {decrypted_file}")
        analyze_decrypted_file(decrypted_file)
        return
    
    print("\n❌ 所有尝试都失败了")
    print("建议:")
    print("1. 密码可能更复杂")
    print("2. 可能需要专业的密码破解工具")
    print("3. 检查是否有其他隐藏的线索")

if __name__ == "__main__":
    main()
