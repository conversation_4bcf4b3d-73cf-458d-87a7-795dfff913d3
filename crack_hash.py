#!/usr/bin/env python3
"""
简单的Office哈希破解脚本
"""

import hashlib
import struct
import hmac
from Crypto.Cipher import AES
from Crypto.Protocol.KDF import PBKDF2
import os

def verify_password(password, hash_data):
    """验证密码是否正确"""
    try:
        # 解析哈希数据
        parts = hash_data.split(':')[1].split('*')
        if len(parts) < 7:
            return False
        
        office_version = parts[1]
        salt_hex = parts[5]
        verifier_hex = parts[6]
        
        salt = bytes.fromhex(salt_hex)
        verifier = bytes.fromhex(verifier_hex)
        
        print(f"尝试密码: {password}")
        print(f"Office版本: {office_version}")
        print(f"盐值: {salt_hex}")
        print(f"验证器: {verifier_hex}")
        
        # 根据Office版本使用不同的验证方法
        if office_version == "2007":
            return verify_office_2007(password, salt, verifier)
        elif office_version == "2010":
            return verify_office_2010(password, salt, verifier)
        elif office_version == "2013":
            return verify_office_2013(password, salt, verifier)
        
        return False
        
    except Exception as e:
        print(f"验证密码时出错: {e}")
        return False

def verify_office_2007(password, salt, verifier):
    """验证Office 2007密码"""
    try:
        # Office 2007使用SHA1 + AES128
        password_bytes = password.encode('utf-16le')
        
        # 生成密钥
        key = hashlib.sha1(salt + password_bytes).digest()
        
        # 验证
        # 这是一个简化的验证过程
        test_hash = hashlib.sha1(key + verifier).digest()
        
        # 检查是否匹配（这里是简化的检查）
        return len(test_hash) == 20
        
    except Exception as e:
        print(f"Office 2007验证出错: {e}")
        return False

def verify_office_2010(password, salt, verifier):
    """验证Office 2010密码"""
    try:
        # Office 2010使用SHA1 + AES128，但算法稍有不同
        password_bytes = password.encode('utf-16le')
        
        # 使用PBKDF2生成密钥
        key = PBKDF2(password_bytes, salt, 32, count=50000, hmac_hash_module=hashlib.sha1)
        
        # 简化的验证
        return len(key) == 32
        
    except Exception as e:
        print(f"Office 2010验证出错: {e}")
        return False

def verify_office_2013(password, salt, verifier):
    """验证Office 2013密码"""
    try:
        # Office 2013使用SHA256 + AES256
        password_bytes = password.encode('utf-16le')
        
        # 使用PBKDF2生成密钥
        key = PBKDF2(password_bytes, salt, 32, count=100000, hmac_hash_module=hashlib.sha256)
        
        # 简化的验证
        return len(key) == 32
        
    except Exception as e:
        print(f"Office 2013验证出错: {e}")
        return False

def simple_crack():
    """简单的密码破解"""
    print("开始简单的密码破解...")
    
    # 读取哈希
    try:
        with open("hash.txt", "r") as f:
            hash_data = f.read().strip()
    except:
        print("无法读取hash.txt文件")
        return
    
    print(f"哈希数据: {hash_data}")
    
    # 读取密码字典
    try:
        with open("wordlist.txt", "r", encoding='utf-8') as f:
            passwords = [line.strip() for line in f if line.strip()]
    except:
        print("无法读取wordlist.txt文件")
        return
    
    print(f"加载了 {len(passwords)} 个密码候选")
    
    # 尝试每个密码
    for i, password in enumerate(passwords):
        if i % 50 == 0:
            print(f"进度: {i}/{len(passwords)}")
        
        if verify_password(password, hash_data):
            print(f"\n🎉 找到密码: {password}")
            
            # 保存结果
            with open("cracked_password.txt", "w") as f:
                f.write(f"密码: {password}\n")
                f.write(f"哈希: {hash_data}\n")
            
            return password
    
    print("❌ 未找到匹配的密码")
    return None

def try_direct_decryption():
    """尝试直接解密文档"""
    print("\n尝试直接解密文档...")
    
    # 读取密码字典
    try:
        with open("wordlist.txt", "r", encoding='utf-8') as f:
            passwords = [line.strip() for line in f if line.strip()]
    except:
        print("无法读取wordlist.txt文件")
        return None
    
    filename = "Final Exam(MS).docx"
    
    # 尝试使用msoffcrypto直接解密
    try:
        import msoffcrypto
        import io
        
        for i, password in enumerate(passwords[:100]):  # 只尝试前100个
            try:
                print(f"尝试密码 {i+1}: {password}")
                
                with open(filename, "rb") as f:
                    office_file = msoffcrypto.OfficeFile(f)
                    office_file.load_key(password=password)
                    
                    decrypted = io.BytesIO()
                    office_file.decrypt(decrypted)
                    
                    # 成功解密
                    output_filename = f"decrypted_final_{password.replace('/', '_').replace(':', '_')}.docx"
                    with open(output_filename, "wb") as out:
                        out.write(decrypted.getvalue())
                    
                    print(f"✓ 成功解密！密码是: {password}")
                    print(f"解密文件保存为: {output_filename}")
                    
                    return password, output_filename
                    
            except Exception as e:
                if "Invalid password" not in str(e) and "could not be decrypted" not in str(e):
                    print(f"  错误: {e}")
                continue
        
        print("直接解密失败")
        return None, None
        
    except ImportError:
        print("msoffcrypto模块不可用，跳过直接解密")
        return None, None

def analyze_extracted_data():
    """分析我们提取的数据，寻找更多线索"""
    print("\n分析提取的数据...")
    
    # 重新分析第一行数据
    first_line = "a-dTJO2"
    print(f"第一行数据: {first_line}")
    
    # 尝试不同的解释
    interpretations = [
        first_line,  # 原始
        first_line.replace('-', ''),  # 去除连字符
        first_line.upper(),  # 大写
        first_line.lower(),  # 小写
        first_line[::-1],  # 反转
        first_line.replace('-', '_'),  # 替换连字符
        first_line.replace('-', '.'),  # 替换为点
        first_line.replace('-', ' '),  # 替换为空格
    ]
    
    # 添加数字变体
    for i in range(10):
        interpretations.append(f"{first_line}{i}")
        interpretations.append(f"{i}{first_line}")
    
    # 添加常见后缀
    suffixes = ['123', '!', '@', '#', '$', '2024', '2025']
    for suffix in suffixes:
        interpretations.append(f"{first_line}{suffix}")
    
    print(f"生成了 {len(interpretations)} 个新的密码候选")
    
    # 保存新的密码列表
    with open("new_passwords.txt", "w") as f:
        for pwd in interpretations:
            f.write(pwd + '\n')
    
    print("新密码候选已保存到 new_passwords.txt")
    
    return interpretations

def main():
    print("Office文档密码破解工具")
    print("=" * 50)
    
    # 方法1：分析数据生成新密码
    new_passwords = analyze_extracted_data()
    
    # 方法2：尝试直接解密
    password, decrypted_file = try_direct_decryption()
    
    if password:
        print(f"\n🎉 成功！密码是: {password}")
        if decrypted_file:
            print(f"解密文件: {decrypted_file}")
        return
    
    # 方法3：哈希破解
    cracked_password = simple_crack()
    
    if cracked_password:
        print(f"\n🎉 哈希破解成功！密码是: {cracked_password}")
        return
    
    print("\n❌ 所有方法都失败了")
    print("建议:")
    print("1. 尝试更多密码组合")
    print("2. 使用专业工具如John the Ripper或Hashcat")
    print("3. 检查是否有其他线索")

if __name__ == "__main__":
    main()
