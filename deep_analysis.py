#!/usr/bin/env python3
"""
深度分析提取的数据，寻找隐藏的密码
"""

import re
import string

def analyze_character_patterns():
    """分析字符模式"""
    print("深度分析字符模式...")
    
    # 读取提取的数据
    with open("complete_extracted.txt", "r", encoding='utf-8') as f:
        content = f.read()
    
    # 分析第一行更仔细
    first_line = content.split('\n')[0] if content else ""
    print(f"第一行详细分析: '{first_line}'")
    
    # 查找所有可能的密码模式
    print("\n查找所有可能的密码模式:")
    
    # 模式1: 字母-数字组合
    pattern1 = re.findall(r'[a-zA-Z]+-[a-zA-Z0-9]+', content)
    if pattern1:
        print(f"模式1 (字母-字母数字): {pattern1}")
    
    # 模式2: 大小写混合的短字符串
    pattern2 = re.findall(r'[a-z][A-Z][a-zA-Z0-9]{2,8}', content)
    if pattern2:
        print(f"模式2 (大小写混合): {pattern2}")
    
    # 模式3: 数字结尾的字符串
    pattern3 = re.findall(r'[a-zA-Z]{2,6}\d{1,3}', content)
    if pattern3:
        print(f"模式3 (字母+数字): {pattern3[:20]}")
    
    # 模式4: 特殊字符分隔的字符串
    pattern4 = re.findall(r'[a-zA-Z0-9][-_:][a-zA-Z0-9]+', content)
    if pattern4:
        print(f"模式4 (特殊字符分隔): {pattern4}")

def analyze_sequence_patterns():
    """分析序列模式"""
    print("\n分析序列模式...")
    
    with open("complete_extracted.txt", "r", encoding='utf-8') as f:
        content = f.read()
    
    # 查找字母序列
    print("字母序列:")
    for i in range(len(content) - 5):
        substr = content[i:i+6]
        if substr.isalpha() and len(set(substr)) >= 4:  # 至少4个不同字母
            print(f"  {substr}")
    
    # 查找数字序列
    print("\n数字序列:")
    numbers = re.findall(r'\d{3,}', content)
    for num in set(numbers):
        if len(num) >= 3:
            print(f"  {num}")

def extract_readable_words():
    """提取可读单词"""
    print("\n提取可读单词...")
    
    with open("complete_extracted.txt", "r", encoding='utf-8') as f:
        content = f.read()
    
    # 提取所有可能的英文单词
    words = re.findall(r'[a-zA-Z]{3,}', content)
    
    # 统计单词频率
    word_count = {}
    for word in words:
        word_lower = word.lower()
        word_count[word_lower] = word_count.get(word_lower, 0) + 1
    
    # 显示最常见的单词
    common_words = sorted(word_count.items(), key=lambda x: x[1], reverse=True)
    
    print("最常见的单词:")
    for word, count in common_words[:30]:
        if count > 1 and len(word) >= 3:
            print(f"  {word}: {count} 次")
    
    # 查找可能的密码相关单词
    password_related = ['pass', 'word', 'key', 'secret', 'code', 'lock', 'open', 'access']
    print("\n密码相关单词:")
    for word, count in common_words:
        if any(related in word.lower() for related in password_related):
            print(f"  {word}: {count} 次")

def analyze_first_bytes():
    """分析第一个数据块的原始字节"""
    print("\n分析第一个数据块的原始字节...")
    
    filename = "Final Exam(MS).docx"
    
    # 重新提取第一个数据块
    with open(filename, 'rb') as f:
        # 第一个位置：大小31，扇区30
        offset = 512 + 30 * 512
        f.seek(offset)
        data = f.read(31)
        
        print(f"第一个数据块 (31字节):")
        print(f"十六进制: {data.hex()}")
        print(f"原始字节: {list(data)}")
        
        # 尝试不同的解码方式
        print("\n尝试不同解码:")
        
        # ASCII
        ascii_chars = ''.join(chr(b) if 32 <= b <= 126 else f'\\x{b:02x}' for b in data)
        print(f"ASCII: {ascii_chars}")
        
        # 尝试XOR解码
        print("\nXOR解码尝试:")
        for key in range(1, 256):
            decoded = bytes(b ^ key for b in data)
            try:
                text = decoded.decode('ascii', errors='ignore')
                if len(text.strip()) > 5 and text.isprintable():
                    print(f"  XOR {key:3d}: {text}")
            except:
                pass
        
        # 尝试移位解码
        print("\n移位解码尝试:")
        for shift in range(1, 26):
            shifted = ""
            for b in data:
                if 65 <= b <= 90:  # A-Z
                    shifted += chr((b - 65 + shift) % 26 + 65)
                elif 97 <= b <= 122:  # a-z
                    shifted += chr((b - 97 + shift) % 26 + 97)
                elif 32 <= b <= 126:  # 其他可打印字符
                    shifted += chr(b)
                else:
                    shifted += '.'
            
            if len(shifted.strip()) > 5:
                print(f"  移位 {shift:2d}: {shifted}")

def look_for_hidden_patterns():
    """寻找隐藏模式"""
    print("\n寻找隐藏模式...")
    
    with open("complete_extracted.txt", "r", encoding='utf-8') as f:
        content = f.read()
    
    # 查找重复的字符序列
    print("重复的字符序列:")
    for length in range(4, 10):
        sequences = {}
        for i in range(len(content) - length):
            seq = content[i:i+length]
            if seq.isalnum():
                sequences[seq] = sequences.get(seq, 0) + 1
        
        repeated = [(seq, count) for seq, count in sequences.items() if count > 1]
        if repeated:
            repeated.sort(key=lambda x: x[1], reverse=True)
            print(f"  长度{length}: {repeated[:5]}")
    
    # 查找可能的Base64编码
    print("\n查找Base64模式:")
    base64_pattern = r'[A-Za-z0-9+/]{8,}={0,2}'
    matches = re.findall(base64_pattern, content)
    for match in matches[:10]:
        if len(match) >= 8:
            print(f"  {match}")
            
            # 尝试解码
            try:
                import base64
                # 添加必要的填充
                padded = match + '=' * (4 - len(match) % 4) if len(match) % 4 else match
                decoded = base64.b64decode(padded)
                decoded_text = decoded.decode('utf-8', errors='ignore')
                if decoded_text.isprintable() and len(decoded_text.strip()) > 2:
                    print(f"    解码: {decoded_text}")
            except:
                pass

def analyze_mathematical_relationships():
    """分析数学关系"""
    print("\n分析数学关系...")
    
    # 我们知道的模式：31, 63, 95, 127 (公差32)
    # 对应扇区：30, 62, 94, 126
    
    print("已知的数学模式:")
    print("大小: 31, 63, 95, 127, 159, 191, 223, 255...")
    print("扇区: 30, 62, 94, 126, 158, 190, 222, 254...")
    
    # 检查这些数字是否有特殊含义
    sizes = [31, 63, 95, 127, 159, 191, 223, 255]
    
    print("\n数字分析:")
    for size in sizes:
        print(f"  {size}: 二进制={bin(size)}, 十六进制={hex(size)}")
        
        # 检查是否是2^n - 1的形式
        for n in range(1, 10):
            if size == 2**n - 1:
                print(f"    -> 2^{n} - 1")
                break
    
    # 31 = 2^5 - 1, 63 = 2^6 - 1, 95 = ?, 127 = 2^7 - 1
    # 这可能是一个线索！

def try_mathematical_passwords():
    """基于数学模式尝试密码"""
    print("\n基于数学模式生成密码...")
    
    # 基于2^n - 1的模式
    powers_of_2_minus_1 = [2**n - 1 for n in range(1, 10)]
    print(f"2^n - 1 序列: {powers_of_2_minus_1}")
    
    # 生成可能的密码
    math_passwords = []
    
    # 使用数字序列
    math_passwords.extend([str(n) for n in powers_of_2_minus_1])
    
    # 使用十六进制
    math_passwords.extend([hex(n)[2:] for n in powers_of_2_minus_1])
    
    # 使用二进制
    math_passwords.extend([bin(n)[2:] for n in powers_of_2_minus_1])
    
    # 组合使用
    math_passwords.extend(['31-63-95-127', '31639527', 'abcd1234'])
    
    print(f"生成的数学密码: {math_passwords}")
    
    # 保存到文件
    with open("math_passwords.txt", "w") as f:
        for pwd in math_passwords:
            f.write(pwd + '\n')
    
    return math_passwords

def main():
    analyze_character_patterns()
    analyze_sequence_patterns()
    extract_readable_words()
    analyze_first_bytes()
    look_for_hidden_patterns()
    analyze_mathematical_relationships()
    math_passwords = try_mathematical_passwords()
    
    print(f"\n总结:")
    print("1. 发现了数学模式：2^n - 1")
    print("2. 第一行可能包含密码线索")
    print("3. 生成了新的密码候选")
    print(f"4. 数学密码保存到: math_passwords.txt")

if __name__ == "__main__":
    main()
