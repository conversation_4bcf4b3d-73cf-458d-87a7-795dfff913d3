# Here are some examples of DYNAMIC.
# Please refer to ./doc/DYNAMIC for documentation on how to set these up.
# Format names up to dynamic_999 are reserved for builtin functions.

####################################################################
# here is a synopsis of the formats in this file.  Please keep this up to date
####################################################################
# dynamic_1001: md5(md5(md5(md5($p))))
# dynamic_1002: md5(md5(md5(md5(md5($p)))))
# dynamic_1003: md5(md5($p).md5($p))
# dynamic_1004: md5(md5(md5(md5(md5(md5($p))))))
# dynamic_1005: md5(md5(md5(md5(md5(md5(md5($p)))))))
# dynamic_1006: md5(md5(md5(md5(md5(md5(md5(md5($p))))))))
# dynamic_1007: md5(md5($p).$s) (vBulletin)
# dynamic_1008: md5($p.$s) (RADIUS User-Password)
# dynamic_1009: md5($s.$p) (RADIUS Responses)
# dynamic_1010: md5($p null_padded_to_len_100) RAdmin v2.x MD5
# dynamic_1011: md5($p.md5($s)) (webEdition CMS)
# dynamic_1012: md5($p.md5($s)) (webEdition CMS)
# dynamic_1013: md5($p.PMD5(username)) (webEdition CMS)
# dynamic_1014: md5($p.$s) (long salt)
# dynamic_1015: md5(md5($p.$u).$s) (PostgreSQL 'pass the hash')
# dynamic_1016: md5($p.$s) (long salt)
# dynamic_1017: md5($s.$p) (long salt)
# dynamic_1018: md5(sha1(sha1($p)))
# dynamic_1019: md5(sha1(sha1(md5($p))))
# dynamic_1020: md5(sha1(md5($p)))
# dynamic_1021: md5(sha1(md5(sha1($p))))
# dynamic_1022: md5(sha1(md5(sha1(md5($p)))))
# dynamic_1023: sha1($p) (hash truncated to length 32)
# dynamic_1024: sha1(md5($p)) (hash truncated to length 32)
# dynamic_1025: sha1(md5(md5($p))) (hash truncated to length 32)
# dynamic_1026: sha1(sha1($p)) (hash truncated to length 32)
# dynamic_1027: sha1(sha1(sha1($p))) (hash truncated to length 32)
# dynamic_1028: sha1(sha1_raw($p)) (hash truncated to length 32)
# dynamic_1029: sha256($p) (hash truncated to length 32)
# dynamic_1030: whirlpool($p) (hash truncated to length 32)
# dynamic_1031: gost($p) (hash truncated to length 32)
# dynamic_1032: sha1_64(utf16($p)) (PeopleSoft)
# dynamic_1033: sha1_64(utf16($p).$s)
# dynamic_1034: md5($p.$u) (PostgreSQL MD5)
# dynamic_1300: md5(md5_raw($p))
# dynamic_1350: md5(md5($s.$p):$s)
# dynamic_1400: sha1(utf16($p)) (Microsoft CREDHIST)
# dynamic_1401: md5($u.\nskyper\n.$p) (Skype MD5)
# dynamic_1501: sha1($s.sha1($p)) (Redmine)
# dynamic_1502: sha1(sha1($p).$s) (XenForo SHA-1)
# dynamic_1503: sha256(sha256($p).$s) (XenForo SHA-256)
# dynamic_1504: sha1($s.$p.$s)
# dynamic_1505: md5($p.$s.md5($p.$s))
# dynamic_1506: md5($u.:XDB:.$p) (Oracle 12c "H" hash)
# dynamic_1507: sha1(utf16($const.$p)) (Mcafee master pass)
# dynamic_1518: md5(sha1($p).md5($p).sha1($p))
# dynamic_1528: sha256($s.$p.$s) (Telegram for Android)
# dynamic_1529: sha1($p null_padded_to_len_32) (DeepSound)
# dynamic_1550: md5($u.:mongo:.$p) (MONGODB-CR system hash)
# dynamic_1551: md5($s.$u.(md5($u.:mongo:.$p)) (MONGODB-CR network hash)
# dynamic_1552: md5($s.$u.(md5($u.:mongo:.$p)) (MONGODB-CR network hash)
# dynamic_1560: md5($s.$p.$s2) (SocialEngine)
# dynamic_1588: sha256($s.sha1($p)) (ColdFusion 11)
# dynamic_1590: sha1(utf16be(space_pad_10(uc($s)).$p)) (IBM AS/400 SHA1)
# dynamic_1592: sha1($s.sha1($s.sha1($p))) (wbb3)
# dynamic_1600: sha1($s.utf16le($p)) (Oracle PeopleSoft PS_TOKEN)
# dynamic_1608: sha256(sha256_raw(sha256_raw($p))) (Neo Wallet)

####################################################################

####################################################################
# Simple DYNAMIC type for md5($p)^^4  (i.e. 4 steps of md5 recursively)
####################################################################
[List.Generic:dynamic_1001]
# expression shown will be the string:   dynamic_1001 md5(md5(md5(md5($p))))
Expression=md5(md5(md5(md5($p))))
Flag=MGF_KEYS_INPUT
Flag=MGF_SET_INP2LEN32
MaxInputLen=55
MaxInputLenX86=110
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__overwrite_from_last_output_to_input2_as_base16_no_size_fix
#if !ARCH_LITTLE_ENDIAN  // unfortunatly, we have no #define here, so we always have to call this function, in a script or they will fail on BE boxes :(
Func=DynamicFunc__set_input2_len_32_cleartop
#endif
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_1001$57200e13b490d4ae47d5e19be026b057:test1
Test=$dynamic_1001$c6cc44f9e7fb7efcde62ba2e627a49c6:thatsworking
Test=$dynamic_1001$0ae9549604e539a249c1fa9f5e5fb73b:test3
# TestM= will ONLY load in an MMX or SSE2 build of JtR.
# TestF= will ONLY load in a non-MMX and nonSSE build (flat oSSL build, or generic)
TestM=$dynamic_1001$94c59ab02fcd39f3ff9a4e553a4afcb6:1234567890123456789012345678901234567890123456789012345
TestF=$dynamic_1001$a8b46c02f1680860622df837fa78c3e4:12345678901234567890123456789012345678901234567890123456789012345678901234567890

####################################################################
# Simple DYNAMIC type for md5($p)^^5  (i.e. 5 steps of md5 recursively)
####################################################################
[List.Generic:dynamic_1002]
# expression shown will be the string:   dynamic_1002 md5(md5(md5(md5(md5($p)))))
Expression=md5(md5(md5(md5(md5($p)))))
Flag=MGF_KEYS_INPUT
Flag=MGF_SET_INP2LEN32
MaxInputLen=55
MaxInputLenX86=110
# here is the optimized 'script' to perform the md5 5 times on itself.
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__overwrite_from_last_output_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt_md5_in2_to_out1
# These are test strings for this format.
Test=$dynamic_1002$25de8cd0b0cf69c5b5bc19c8ee64adab:test1
Test=$dynamic_1002$a0b535420ea47849f7c2cc09a3ad0ac3:thatsworking
Test=$dynamic_1002$4cb029bd5b4ef79f785ca685caf17bf8:test3
TestM=$dynamic_1002$5a791c6c9de2f488a8155f35900348b0:1234567890123456789012345678901234567890123456789012345
TestF=$dynamic_1002$b8da59d26b6494df42b8c0f1fba8cd7e:12345678901234567890123456789012345678901234567890123456789012345678901234567890

####################################################################
# Simple DYNAMIC type for md5(md5($p).md5($p))
####################################################################
[List.Generic:dynamic_1003]
# expression shown will be the string:   dynamic_1003 md5(md5($p).md5($p))
Expression=md5(md5($p).md5($p))
# NOTE, this format does NOT work on SSE2.  It requires a md5() of a 64 byte string.
# SSE (or MMX) is limtited to 54 byte max password, due to 'enhancements'
# Thus, we need a non-sse2 safe flag.
##JF Flag=MGF_NOTSSE2Safe
##JF Flag=MGF_KEYS_INPUT
##JF Flag=MGF_FULL_CLEAN_REQUIRED
# here is the optimized 'script' to perform hash 'like' IPB but salt replaced with password.
##JF Func=DynamicFunc__crypt_md5
##JF Func=DynamicFunc__clean_input2_kwik
##JF Func=DynamicFunc__append_from_last_output_to_input2_as_base16
##JF Func=DynamicFunc__append_from_last_output_to_input2_as_base16
##JF Func=DynamicFunc__crypt_md5_in2_to_out1

# much more optimal.   From 1118k to 2155k on an SSE2 box.
Flag=MGF_FLAT_BUFFERS
Flag=MGF_KEYS_BASE16_IN1
Flag=MGF_POOR_OMP
MaxInputLen=110
MaxInputLenX86=110
Func=DynamicFunc__append_input_from_input
Func=DynamicFunc__MD5_crypt_input1_to_output1_FINAL

# These are test strings for this format.
Test=$dynamic_1003$478b10974f15e7295883224fd286ccba:test1
Test=$dynamic_1003$18a59101e6c6fb38260d542a394ecb22:thatsworking
Test=$dynamic_1003$630b01b68b6db6fd43a751f8147d1faf:test3
Test=$dynamic_1003$2dbecd858c29d5602da78204af7dfe1b:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

####################################################################
# Simple DYNAMIC type for md5($p)^^6  (i.e. 6 steps of md5 recursively)
####################################################################
[List.Generic:dynamic_1004]
# expression shown will be the string:   dynamic_1004 md5(md5(md5(md5(md5(md5($p))))))
Expression=md5(md5(md5(md5(md5(md5($p))))))
Flag=MGF_KEYS_INPUT
Flag=MGF_SET_INP2LEN32
MaxInputLen=55
MaxInputLenX86=110
# here is the optimized 'script' to perform the md5 6 times on itself.
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__overwrite_from_last_output_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt_md5_in2_to_out1
# These are test strings for this format.
Test=$dynamic_1004$de1b991dd27fb9813e88b957a455dccd:test1
Test=$dynamic_1004$6a62cd3c4d81139f61fb2553cdef0dc7:thatsworking
Test=$dynamic_1004$a977990e521c5d1d17c6d65fdf2681b4:test3
TestM=$dynamic_1004$e475d31b00626080fc01ca4832a33293:1234567890123456789012345678901234567890123456789012345
TestF=$dynamic_1004$f60eca1ad34608b7c6b1b04379b3fee3:12345678901234567890123456789012345678901234567890123456789012345678901234567890


####################################################################
# Simple DYNAMIC type for md5($p)^^7  (i.e. 7 steps of md5 recursively)
####################################################################
[List.Generic:dynamic_1005]
# expression shown will be the string:   dynamic_1005 md5(md5(md5(md5(md5(md5(md5($p)))))))
Expression=md5(md5(md5(md5(md5(md5(md5($p)))))))
Flag=MGF_KEYS_INPUT
Flag=MGF_SET_INP2LEN32
MaxInputLen=55
MaxInputLenX86=110
# here is the optimized 'script' to perform the md5 7 times on itself.
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__overwrite_from_last_output_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt_md5_in2_to_out1
# These are test strings for this format.
Test=$dynamic_1005$784c527d0d92873ff9c0773e1c35621d:test1
Test=$dynamic_1005$efcbbe6331caecf0e7f40160e65aadcc:thatsworking
Test=$dynamic_1005$abb8bdd2c6ac2dfea2b2af6f5aed5446:test3
TestM=$dynamic_1005$8f853f8abf74a8e686c213a9849d9beb:1234567890123456789012345678901234567890123456789012345
TestF=$dynamic_1005$37e4fc15b5dc59286aee85f4b7008315:12345678901234567890123456789012345678901234567890123456789012345678901234567890

####################################################################
# Simple DYNAMIC type for md5($p)^^8  (i.e. 8 steps of md5 recursively)
####################################################################
[List.Generic:dynamic_1006]
# expression shown will be the string:   dynamic_1006 md5(md5(md5(md5(md5(md5(md5(md5($p))))))))
Expression=md5(md5(md5(md5(md5(md5(md5(md5($p))))))))
Flag=MGF_KEYS_INPUT
Flag=MGF_SET_INP2LEN32
MaxInputLen=55
MaxInputLenX86=110
# here is the optimized 'script' to perform the md5 8 times on itself.
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__overwrite_from_last_output_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__overwrite_from_last_output2_to_input2_as_base16_no_size_fix
Func=DynamicFunc__set_input2_len_32_cleartop
Func=DynamicFunc__crypt_md5_in2_to_out1
# These are test strings for this format.
Test=$dynamic_1006$1ec1f32398f64cab51183f63630eceea:test1
Test=$dynamic_1006$f66b339ac21d6fd6af216f2b70aab2c9:thatsworking
Test=$dynamic_1006$e9d38522b5eeec753332e576e2e0fe5d:test3
TestM=$dynamic_1006$399310c857c0d83b931441d514528ee6:1234567890123456789012345678901234567890123456789012345
TestF=$dynamic_1006$e89d92a2291b5b43b6697c51d722ae8b:12345678901234567890123456789012345678901234567890123456789012345678901234567890

####################################################################
# Simple DYNAMIC type for vBulletin md5(md5($p).$s)   Included here to 'exercise' the script parser
####################################################################
[List.Generic:dynamic_1007]
# expression shown will be the string:   dynamic_1007 md5(md5($p).$s) [vBulletin]
Expression=md5(md5($p).$s) (vBulletin)
# Flag needed here, is Salt.  There is no 'fixed' saltlen.
Flag=MGF_SALTED
Flag=MGF_KEYS_BASE16_IN1
# vBulletin has a 'fixed' 3 byte salt, so list the fixed size  (restriction removed).
SaltLen=-23
SaltLenX86=-64
MaxInputLen=55
MaxInputLenX86=110
# here is the optimized 'script' to perform vBulletin hash
Func=DynamicFunc__set_input_len_32_cleartop
Func=DynamicFunc__append_salt
Func=DynamicFunc__crypt_md5
Test=$dynamic_1007$daa61d77e218e42060c2fa198ac1feaf$SXB:test1
Test=$dynamic_1007$de56b00bb15d6db79204bd44383469bc$T &:thatsworking
Test=$dynamic_1007$fb685c6f469f6e549c85e4c1fb5a65a6$HEX$5C483A:test3
Test=$dynamic_1007$5dd8145e0d1e2499bce05dcb4bce5cdf$HEX$24324F:testme
TestM=$dynamic_1007$09019afd1303ff078ba323569ac05ea5$123:1234567890123456789012345678901234567890123456789012
TestF=$dynamic_1007$1eff62d90df7e82566f75f7cfb316f6e$PS9:12345678901234567890123456789012345678901234567890123456789012345678901234567890

####################################################################
# Dynamic type for algorithm used in RADIUS User-Password attribute md5($p.$s)
####################################################################
[List.Generic:dynamic_1008]
# expression shown will be this string:
Expression=md5($p.$s) (RADIUS User-Password)
# Flag needed here, is Salt
Flag=MGF_SALTED
# The salt has a fixed length of 16 bytes
Saltlen=16
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_salt
Func=DynamicFunc__crypt_md5
Test=$dynamic_1008$b962b0d40fc9111ce5f8efab424bad73$NormalSaltNormal:secret
Test=$dynamic_1008$8bfccd9d67ec0bcdc38e9ae3c19a2903$FinishingwitHEX$:secret
Test=$dynamic_1008$bf239357f3aa95508a53fe41b7e5f2e3$inthem$HEXiddle6:secret
# unfortunately, these next 2 have embedded NULLs, so at this time they have been removed.
# later we will get dynamic working with these also.
#Test=$dynamic_1008$7fe3c4d1bf2ac68e94ee9f2bf75b9601$HEX$00000000000000000000000000000000:secret
#Test=$dynamic_1008$658bbf9f04538d6bede09a4a52a77504$HEX$626c6168003637383930313233343536:secret
TestM=$dynamic_1008$6bf84723242c758538951ebfcbe82498$Zm8EXfUeRrEJMx5b:123456789012345678901234567890123456789
TestF=$dynamic_1008$7978620b9b48b1d6e322bfe5b081bf3e$yH9RErqH2ktDYesl:1234567890123456789012345678901234567890123456789012345678901234

######################################################################
# Dynamic Type for algorithm used in RADIUS Responses md5($s.$p)
#
# Also used by a "popular" backup solution
# select id, name, emailid, password from administrator;
# hashlib.md5((str(id) + pwd)).hexdigest()
######################################################################
[List.Generic:dynamic_1009]
Expression=md5($s.$p) (RADIUS Responses)
Flag=MGF_SALTED
Saltlen=-16
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Test=$dynamic_1009$0b9b9fdf75fc79d85c5b69aa1de26288$Salt:test1
Test=$dynamic_1009$05ed3fc5e044d559290c400254e568c9$1:hackme
TestM=$dynamic_1009$9619094908f5c9f29eb95eadefae84c3$ex5fKtjhZwVMCi2C:123456789012345678901234567890123456789
TestF=$dynamic_1009$92cfbd6aadc48b2ef97ca2699037dea6$73WkPYCT2CxnQ8pt:1234567890123456789012345678901234567890123456789012345678901234

######################################################################
# Dynamic Type for algorithm used in RAdmin v2.x Responses md5($p.NULL-to-100-bytes)
# v2, where keys are in input, and set_input_len_100 'cleans' up if needed.
######################################################################
[List.Generic:dynamic_1010]
Expression=md5($p null_padded_to_len_100) RAdmin v2.x MD5
##JF Flag=MGF_NOTSSE2Safe
##JF Flag=MGF_KEYS_INPUT
##JF Func=DynamicFunc__set_input_len_100
##JF Func=DynamicFunc__crypt_md5

# MUCH faster.  Went from 1930k to 5600k
MaxInputLen=99
MaxInputLenX86=99
Flag=MGF_FLAT_BUFFERS
Flag=MGF_KEYS_INPUT
Flag=MGF_POOR_OMP
Func=DynamicFunc__set_input_len_100
Func=DynamicFunc__MD5_crypt_input1_to_output1_FINAL

Test=$dynamic_1010$B137F09CF92F465CABCA06AB1B283C1F:lastwolf
Test=$dynamic_1010$14e897b1a9354f875df51047bb1a0765:podebradka
Test=$dynamic_1010$02ba5e187e2589be6f80da0046aa7e3c:12345678
Test=$dynamic_1010$b4e13c7149ebde51e510959f30319ac7:firebaLL
Test=$dynamic_1010$3d2c8cae4621edf8abb081408569482b:yamaha12345
Test=$dynamic_1010$60cb8e411b02c10ecc3c98e29e830de8:xplicit

####################################################################
# DYNAMIC type for webEdition CMS md5($p.md5($s))
# > select username,passwd,UseSalt from tblUser
# username is salt
####################################################################
[List.Generic:dynamic_1011]
Expression=md5($p.md5($s)) (webEdition CMS)
Flag=MGF_SALTED
MaxInputLenX86=48
SaltLen=-55
MaxInputLen=23
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_1011$e82bf09e8a1899d4c3d00a3f380d5cdb$SXB:openwall
Test=$dynamic_1011$c0e024d9200b5705bc4804722636378a$admin:admin
Test=$dynamic_1011$14f8b3781f19a3b7ea520311482ce207$openwall:openwall
TestM=$dynamic_1011$b8db62204359efcbfc92da2d697d21cb$xkcR9B:12345678901234567890123
TestF=$dynamic_1011$61f55f04f8f4e05392415181bcf57420$rtJEIj:123456789012345678901234567890123456789012345678

####################################################################
# DYNAMIC type for webEdition CMS md5($p.md5($s))
# > select username,passwd,UseSalt from tblUser
# username is salt
# Twice as fast as dynamic_1011 since md5($s) is pre-computed!
####################################################################
[List.Generic:dynamic_1012]
Expression=md5($p.md5($s)) (webEdition CMS)
Flag=MGF_SALTED
Flag=MGF_SALT_AS_HEX
MaxInputLenX86=48
SaltLen=-110
MaxInputLen=23
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_salt
Func=DynamicFunc__crypt_md5
Test=$dynamic_1012$e82bf09e8a1899d4c3d00a3f380d5cdb$SXB:openwall
Test=$dynamic_1012$c0e024d9200b5705bc4804722636378a$admin:admin
Test=$dynamic_1012$14f8b3781f19a3b7ea520311482ce207$openwall:openwall
TestM=$dynamic_1012$b8db62204359efcbfc92da2d697d21cb$xkcR9B:12345678901234567890123
TestF=$dynamic_1012$61f55f04f8f4e05392415181bcf57420$rtJEIj:123456789012345678901234567890123456789012345678

####################################################################
## DYNAMIC type for webEdition CMS md5($p.PMD5(username))
## > select md5(username),passwd,UseSalt from tblUser
## PMD5(username), pre-computed md5 of username is salt
#####################################################################
[List.Generic:dynamic_1013]
Expression=md5($p.PMD5(username)) (webEdition CMS)
Flag=MGF_SALTED
MaxInputLenX86=48
MaxInputLen=23
SaltLen=32
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_salt
Func=DynamicFunc__crypt_md5
Test=$dynamic_1013$14f8b3781f19a3b7ea520311482ce207$f2df0ddd3129c68b1ae7be05779ebeb3:openwall
TestM=$dynamic_1013$b8db62204359efcbfc92da2d697d21cb$f3ae4d2b2c3600df57bbeab163eac04b:12345678901234567890123
TestF=$dynamic_1013$61f55f04f8f4e05392415181bcf57420$5e87dbf3663cbead467fc645c5c9586d:123456789012345678901234567890123456789012345678

####################################################################
# Dynamic type for md5($p.$s) for long salts
####################################################################
[List.Generic:dynamic_1014]
# expression shown will be this string:
Expression=md5($p.$s) (long salt)
# Flag needed here, is Salt
Flag=MGF_SALTED
##JF Went from 1376k/1100k to 3483k/2600k by switching to flat buffer sse2
##JF Flag=MGF_NOTSSE2Safe
Flag=MGF_FLAT_BUFFERS
##JF Flag=MGF_FULL_CLEAN_REQUIRED
##JF MaxInputLen=32
MaxInputLenX86=110
MaxInputLen=110
SaltLen=-137
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_salt
##JF Func=DynamicFunc__crypt_md5
Func=DynamicFunc__MD5_crypt_input1_to_output1_FINAL
Test=$dynamic_1014$c0dbfba522fad4054da9808a2fa09580$aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa:test
Test=$dynamic_1014$6130b0e84d387ffd460fc83cffcc1426$bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbc:aaaa
Test=$dynamic_1014$399df23011bb3742e83011c1074187e2$cccccccccccccccccccccccccccccccccccccccccccccccccccd:bbbb
Test=$dynamic_1014$b962b0d40fc9111ce5f8efab424bad73$NormalSaltNormal:secret
Test=$dynamic_1014$8bfccd9d67ec0bcdc38e9ae3c19a2903$FinishingwitHEX$:secret
Test=$dynamic_1014$bf239357f3aa95508a53fe41b7e5f2e3$inthem$HEXiddle6:secret
Test=$dynamic_1014$e463b65f14643afd970c7ea7e7efeb0f$123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890:12345678901234567890123456789012

####################################################################
# Dynamic type for md5(md5($p.$u).$s) for PostgreSQL 'pass the hash' weakness
# See also dynamic_1034 for PostgreSQL MD5
# http://www.openwall.com/lists/oss-security/2015/03/03/12
####################################################################
[List.Generic:dynamic_1015]
Expression=md5(md5($p.$u).$s) (PostgreSQL 'pass the hash')
Flag=MGF_SALTED
Flag=MGF_USERNAME
MaxInputLen=31
MaxInputLenX86=56
SaltLen=-23
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_userid
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__append_salt2
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_1015$1d586cc8d137e5f1733f234d224393e8$HEX$f063f05d:openwall:postgres
Test=$dynamic_1015$1c4e11fb51835c3bbe9851ec91ec1375$HEX$c31803a2:password:postgres
Test=$dynamic_1015$bf2a64f35feba7bf1b633d60393c1356$HEX$684697c8:openwall:postgres
# repeat one test in the format that is used in john.pot
Test=$dynamic_1015$1d586cc8d137e5f1733f234d224393e8$HEX$f063f05d242455706f737467726573:openwall
TestM=$dynamic_1015$c99b3494687ed9895d4ffca184a9daf5$M6krNt:1234567890123456789012345678901:usrx
TestF=$dynamic_1015$5618a66e934dfef13cae2d06d71bdf75$usrwxT:12345678901234567890123456789012345678901234567890123456:01234

####################################################################
# Dynamic type for md5($p.$s) for long salts
#  NOTE, we should use dynamic_2001 and not this hash.
####################################################################
[List.Generic:dynamic_1016]
# expression shown will be this string:
Expression=md5($p.$s) (long salt)
# Flag needed here, is Salt
Flag=MGF_SALTED
Flag=MGF_FLAT_BUFFERS
MaxInputLenX86=110
MaxInputLen=110
SaltLen=-137
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_salt
Func=DynamicFunc__MD5_crypt_input1_to_output1_FINAL
Test=$dynamic_1016$08e3ded271f83affc8f127dae3cb5bed$HEX$e30003fa000100000001000000000000000000000000000000000000000000000000000000000000d7dd1060ee06bec2:secret
# repeat that hash in exactly the same form that is used in john.pot
#Test=$dynamic_1016$08e3ded271f83affc8f127dae3cb5bed$HEX$48455824653330303033666130303031303030303030303130303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303064376464313036306565303662656332:secret

####################################################################
# Dynamic type for md5($s.$p) for long salts
#  NOTE, we should use dynamic_2004 and not this hash.
####################################################################
[List.Generic:dynamic_1017]
# expression shown will be this string:
Expression=md5($s.$p) (long salt)
# Flag needed here, is Salt
Flag=MGF_SALTED
Flag=MGF_FLAT_BUFFERS
MaxInputLenX86=55
MaxInputLen=55
SaltLen=-192
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_keys
Func=DynamicFunc__MD5_crypt_input1_to_output1_FINAL
# PrestaShop uses long salts, $s == _COOKIE_KEY_ (config/settings.inc.php file)
# PrestaShop hashes can be extracted from the "ps_employee" table ("ps_" is the default table prefix)
# PrestaShop 1.6.0.9 was used for testing this!
# Update: PrestaShop 1.7.x.y doesn't make use of the config/settings.inc.php
# file. It uses bcrypt hashing, and the hashes are stored in the ps_customer
# table.
#
# This hash format is also used by RADIUS Responses when salts are > 16 bytes long.
Test=$dynamic_1017$2b3f4811983db00560dfd4c28f67bc5a$B3DdR7ZVi2N26aVbR84bjSAHht8JYhqcDr1FK49jiQXFU8Vo66PKmAFt:lemons12345

[List.Generic:dynamic_1018]
Expression=md5(sha1(sha1($p)))
Flag=MGF_StartInX86Mode
Flag=MGF_KEYS_INPUT
MaxInputLen=55
MaxInputLenX86=110
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__SHA1_crypt_input1_append_input2
Func=DynamicFunc__SHA1_crypt_input2_overwrite_input2
Func=DynamicFunc__X86toSSE_switch_input2
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_1018$a93dcf04edd0e2b98c1165304c250b80:1234abcd
Test=$dynamic_1018$f3b5f01810c4d66ae0af85b3789e12cd:potato
TestM=$dynamic_1018$5c43d21a3dfb81435d45e78334fa6109:1234567890123456789012345678901234567890123456789012345
TestF=$dynamic_1018$073c8ec8e73fdedb7aad9df4ded29ba3:12345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_1019]
Expression=md5(sha1(sha1(md5($p))))
Flag=MGF_KEYS_INPUT
MaxInputLen=55
MaxInputLenX86=110
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__SSEtoX86_switch_output1
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__SHA1_crypt_input2_overwrite_input2
Func=DynamicFunc__SHA1_crypt_input2_overwrite_input2
Func=DynamicFunc__X86toSSE_switch_input2
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_1019$86f607194f0aefe63a6c13723e94382d:jjaammaaiiccaa
Test=$dynamic_1019$77faf9282c0c9b5870a4d9c3ec484aca:blink182
TestM=$dynamic_1019$bc679e2715c335fcf8b9205efd031521:1234567890123456789012345678901234567890123456789012345
TestF=$dynamic_1019$36966d66615d3c0de89ca53ed88212ec:12345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_1020]
Expression=md5(sha1(md5($p)))
Flag=MGF_KEYS_INPUT
MaxInputLen=55
MaxInputLenX86=110
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__SSEtoX86_switch_output1
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__SHA1_crypt_input2_overwrite_input2
Func=DynamicFunc__X86toSSE_switch_input2
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_1020$2a8ce40b837c8550506d9b5d220bac28:0124
TestM=$dynamic_1020$74102b324b8b1cf909263284a53955aa:1234567890123456789012345678901234567890123456789012345
TestF=$dynamic_1020$e4ad9c1e34bad775d2cd399294c286e8:12345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_1021]
Expression=md5(sha1(md5(sha1($p))))
Flag=MGF_StartInX86Mode
Flag=MGF_KEYS_INPUT
MaxInputLen=55
MaxInputLenX86=110
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__SHA1_crypt_input1_append_input2
Func=DynamicFunc__X86toSSE_switch_input2
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__SSEtoX86_switch_output2
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__SHA1_crypt_input2_overwrite_input2
Func=DynamicFunc__X86toSSE_switch_input2
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_1021$c1e054140feac1b411d3efc8bae5b881:norway
TestM=$dynamic_1021$dbf8fc7a96898e16e1251d94b3bb06d9:1234567890123456789012345678901234567890123456789012345
TestF=$dynamic_1021$df38670077cb4c299bcaf06e8271c986:12345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_1022]
Expression=md5(sha1(md5(sha1(md5($p)))))
Flag=MGF_KEYS_INPUT
MaxInputLen=55
MaxInputLenX86=110
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__SSEtoX86_switch_output1
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__SHA1_crypt_input2_overwrite_input2
Func=DynamicFunc__X86toSSE_switch_input2
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__SSEtoX86_switch_output2
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__SHA1_crypt_input2_overwrite_input2
Func=DynamicFunc__X86toSSE_switch_input2
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_1022$9caf8c249c588a89030db581ec6cea47:313131
Test=$dynamic_1022$e1eb34c6ab9e9cbe4ff67fdeb747e169:8616
TestM=$dynamic_1022$d4d51c756abefb41bafbcff7c6237618:1234567890123456789012345678901234567890123456789012345
TestF=$dynamic_1022$9367b878de004be863000174e728c15f:12345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_1023]
Expression=sha1($p) (hash truncated to length 32)
Flag=MGF_KEYS_INPUT
Flag=MGF_FLAT_BUFFERS
Flag=MGF_POOR_OMP
MaxInputLen=110
MaxInputLenX86=110
Func=DynamicFunc__SHA1_crypt_input1_to_output1_FINAL
Test=$dynamic_1023$5baa61e4c9b93f3f0682250b6cf8331b:password
Test=$dynamic_1023$e4227954acdafb57977d7dc8a1957095:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_1024]
Expression=sha1(md5($p)) (hash truncated to length 32)
Flag=MGF_KEYS_INPUT
MaxInputLen=55
MaxInputLenX86=110
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__SSEtoX86_switch_output1
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__SHA1_crypt_input2_to_output1_FINAL
Test=$dynamic_1024$c56289182ffd862d906eac1ce5c6fe6d:trigun
TestM=$dynamic_1024$e290c79e9584e4cd61faded848ff96f0:1234567890123456789012345678901234567890123456789012345
TestF=$dynamic_1024$609fed73c093edfbcc9913004656f360:12345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_1025]
Expression=sha1(md5(md5($p))) (hash truncated to length 32)
Flag=MGF_KEYS_INPUT
MaxInputLen=55
MaxInputLenX86=110
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__crypt2_md5
Func=DynamicFunc__SSEtoX86_switch_output2
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_from_last_output2_as_base16
Func=DynamicFunc__SHA1_crypt_input2_to_output1_FINAL
Test=$dynamic_1025$f122db007ed655921f98184e4302bba8:123456
TestM=$dynamic_1025$006d246968ee9e761578bce26d5a82a2:1234567890123456789012345678901234567890123456789012345
TestF=$dynamic_1025$cc98637054045e998ab01e97ce65585e:12345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_1026]
Expression=sha1(sha1($p)) (hash truncated to length 32)
Flag=MGF_FLAT_BUFFERS
Flag=MGF_KEYS_INPUT
MaxInputLen=110
MaxInputLenX86=110
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__SHA1_crypt_input1_overwrite_input2
Func=DynamicFunc__SHA1_crypt_input2_to_output1_FINAL
Test=$dynamic_1026$71b37a2d9b0a7d5dc4da8a08d9092817:peanuts
Test=$dynamic_1026$30f8cf133eaac8e3b6af4bcba722921d:peanut
Test=$dynamic_1026$809df50e02b68a389a8f6639a03421eb:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_1027]
Expression=sha1(sha1(sha1($p))) (hash truncated to length 32)
Flag=MGF_FLAT_BUFFERS
Flag=MGF_KEYS_INPUT
MaxInputLen=110
MaxInputLenX86=110
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__SHA1_crypt_input1_overwrite_input2
Func=DynamicFunc__SHA1_crypt_input2_overwrite_input2
Func=DynamicFunc__SHA1_crypt_input2_to_output1_FINAL
Test=$dynamic_1027$b8443c12b3066dac22b3857b2fb779b4:leelee
Test=$dynamic_1027$00aeb6dc5e6269a6b2f39728cd8a6812:test1
Test=$dynamic_1027$54e45916fb79f7be1c695828fdba4491:test3
Test=$dynamic_1027$d08a9796dc4ea6decf59ce43caa1b4b4:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_1028]
Expression=sha1(sha1_raw($p)) (hash truncated to length 32)
# currently, the raw sha1 does not work in SSE code.  It does work on 'flat' x86 code
Flag=MGF_FLAT_BUFFERS
Flag=MGF_KEYS_INPUT
MaxInputLen=110
MaxInputLenX86=110
Func=DynamicFunc__clean_input2
Func=DynamicFunc__LargeHash_OUTMode_raw
Func=DynamicFunc__SHA1_crypt_input1_append_input2
Func=DynamicFunc__SHA1_crypt_input2_to_output1_FINAL
Test=$dynamic_1028$79239e0207cd5f6a472c8795c73b451d:rainbow
Test=$dynamic_1028$06c0bf5b64ece2f648b5f048a7190390:test1
Test=$dynamic_1028$f357e78cabad76fd3f1018ef85d78499:test3
Test=$dynamic_1028$64ad70ca481a2c33a2c843cc03555365:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_1029]
Expression=sha256($p) (hash truncated to length 32)
Flag=MGF_FLAT_BUFFERS
MaxInputLen=110
MaxInputLenX86=110
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__SHA256_crypt_input1_to_output1_FINAL
Test=$dynamic_1029$e4ad93ca07acb8d908a3aa41e920ea4f:iloveyou
Test=$dynamic_1029$13b1f7ec5beaefc781e43a3b344371cd:freedom
Test=$dynamic_1029$aa97302150fce811425cd84537028a5a:computer
Test=$dynamic_1029$75ff6bea5b0ad25171988e435c24b3ee:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_1030]
Expression=whirlpool($p) (hash truncated to length 32)
Flag=MGF_FLAT_BUFFERS
MaxInputLen=110
MaxInputLenX86=110
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__WHIRLPOOL_crypt_input1_to_output1_FINAL
Test=$dynamic_1030$56fd4ecb153a08b65a73b51e3c8ca369:spiral
Test=$dynamic_1030$6b116ef0c32185d3ae1136f4593a5cae:defender
Test=$dynamic_1030$fee8605795f28dda386324d59a28ba99:amazon
Test=$dynamic_1030$73622582350099f45647970c0a8a2496:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_1031]
Expression=gost($p) (hash truncated to length 32)
Flag=MGF_FLAT_BUFFERS
MaxInputLen=110
MaxInputLenX86=110
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__GOST_crypt_input1_to_output1_FINAL
Test=$dynamic_1031$0e8cd409a23c2e7ad1c5b22b101dfa16:admin
Test=$dynamic_1031$3b024be97641061bdd5409b4866c26c5:test1
Test=$dynamic_1031$55719211936152fbe2e1f6aa796fa866:test3
Test=$dynamic_1031$096dd6ff632727d682070752fbda548e:12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_1032]
Expression=sha1_64(utf16($p)) (PeopleSoft)
Flag=MGF_INPBASE64m
Flag=MGF_FLAT_BUFFERS
Flag=MGF_UTF8
MaxInputLen=110
MaxInputLenX86=110
Func=DynamicFunc__clean_input
Func=DynamicFunc__setmode_unicode
Func=DynamicFunc__append_keys
Func=DynamicFunc__SHA1_crypt_input1_to_output1_FINAL
Test=$dynamic_1032$6Pl/upEE0epQR5SObftn+s2fW3M=:password

[List.Generic:dynamic_1033]
Expression=sha1_64(utf16($p).$s)
Flag=MGF_INPBASE64m
Flag=MGF_FLAT_BUFFERS
Flag=MGF_SALTED
Flag=MGF_UTF8
SaltLen=-32
MaxInputLen=110
MaxInputLenX86=110
Func=DynamicFunc__clean_input
Func=DynamicFunc__setmode_unicode
Func=DynamicFunc__append_keys
Func=DynamicFunc__setmode_normal
Func=DynamicFunc__append_salt
Func=DynamicFunc__SHA1_crypt_input1_to_output1_FINAL
# we want to make SURE that something ending with = mixed
# with others NOT ending with = are handled properly.
Test=$dynamic_1033$D7C1gHanUq1xE96HpEQitzAhNB8$FyKXs6zU:password
Test=$dynamic_1033$sh+Q50Cp4vERzDkJcaaKIv8zubM=$M1RxMCTZ:password2
Test=$dynamic_1033$DfM7ryjrNamyG0wRS6CwheZS6Mo$3swBL4qn:

####################################################################
# Dynamic type for md5($p.$u) for PostgreSQL stored MD5 hashes
# See also dynamic_1015 for PostgreSQL 'pass the hash' (with salt)
####################################################################
[List.Generic:dynamic_1034]
Expression=md5($p.$u) (PostgreSQL MD5)
Flag=MGF_USERNAME
SaltLen=-32
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_userid
Func=DynamicFunc__crypt_md5
Test=$dynamic_1034$bd6fd49a627ecdbe4031b2d52d5748ab:openwall:postgres
Test=$dynamic_1034$32e12f215ba27cb750c9e093ce4b5127:password:postgres

[List.Generic:dynamic_1300]
MaxInputLen=55
MaxInputLenX86=110
Flag=MGF_POOR_OMP
Expression=md5(md5_raw($p))
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
# changed these 3 lines to the 4 lines that follow. This format has had problems
# in certain builds (like generic). Likely it is the set_input_len_16 causing
# issues and should be looked at. For now, the new method using input2 works fine.
#Func=DynamicFunc__crypt_md5_to_input_raw
#Func=DynamicFunc__set_input_len_16
#Func=DynamicFunc__crypt_md5
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append2_from_last_output1_as_raw
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_1300$43442676c74ae59f219c2d87fd6bad52:admin
Test=$dynamic_1300$5cbaca32e76bb49ca69657a9145d77ee:test1
Test=$dynamic_1300$1c8b12da6f307bbfe8d245c79d468b3d:test3
TestM=$dynamic_1300$60f3fd93d4e949d871dc7713664b2b4e:1234567890123456789012345678901234567890123456789012345
TestF=$dynamic_1300$d66e6e66ff4a8dc6f3665740268fe1bc:12345678901234567890123456789012345678901234567890123456789012345678901234567890

[List.Generic:dynamic_1350]
Expression=md5(md5($s.$p):$s)
# Flag needed here, is Salt.
CONST1=:
Flag=MGF_SALTED
SaltLen=2
MaxInputLen=53
MaxInputLenX86=108
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_from_last_output_as_base16
Func=DynamicFunc__append_input1_from_CONST1
Func=DynamicFunc__append_salt
Func=DynamicFunc__crypt_md5
Test=$dynamic_1350$c1f58952ab714b5ef76926628f6e0b16$92:blondie
Test=$dynamic_1350$a130dbe6709653d602eec70945e14f87$9e:blondie
TestM=$dynamic_1350$0f7dcf84c95a3c191a4bff15c62058a0$12:12345678901234567890123456789012345678901234567890123
TestF=$dynamic_1350$f78bdbc1c68b64f52c40d777068309fb$12:123456789012345678901234567890123456789012345678901234567890123456789012345678

# Thanks to JimF for his help in making this format work
# (Jean-Michel Picod)
[List.Generic:dynamic_1400]
Expression=sha1(utf16($p)) (Microsoft CREDHIST)
Flag=MGF_INPUT_20_BYTE
Flag=MGF_StartInX86Mode
Flag=MGF_POOR_OMP
Flag=MGF_UTF8
MaxInputLen=55
MaxInputLenX86=110
Func=DynamicFunc__clean_input
Func=DynamicFunc__setmode_unicode
Func=DynamicFunc__append_keys
Func=DynamicFunc__SHA1_crypt_input1_to_output1_FINAL
#Test=$dynamic_1500$e8f97fba9104d1ea5047948e6dfb67fa:password
Test=$dynamic_1400$e8f97fba9104d1ea5047948e6dfb67facd9f5b73:password

# Thanks JimF for his help making this format to work
# (Jean-Michel Picod)
[List.Generic:dynamic_1401]
Expression=md5($u.\nskyper\n.$p) (Skype MD5)
Flag=MGF_USERNAME
Flag=MGF_StartInX86Mode
Flag=MGF_INPUT_20_BYTE
CONST1=\x0Askyper\x0A
# 23 gives us ability to do user names up to 55-8-23 (or 24 byte user names)
# this should be ported to a flat format.
MaxInputLen=23
MaxInputLenX86=110
SaltLen=-24
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_userid
Func=DynamicFunc__append_input1_from_CONST1
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt
# NOTE, I did not have full 40 byte hashes, but am using the INPUT_20_BYTE flag.
# The last 8 0's are only used for valid to work, and so we can add full hashes
# when we get them. Only the first 16 bytes is used in hash compare within JtR
Test=$dynamic_1401$27f6a9d892475e6ce0391de8d2d893f700000000:password:username
Test=$dynamic_1401$27f6a9d892475e6ce0391de8d2d893f700000000$$Uusername:password
# repeat that hash in exactly the same form that is used in john.pot
Test=$dynamic_1401$27f6a9d892475e6ce0391de8d2d893f700000000$HEX$2455757365726e616d65:password

# In Redmine, the hashed password is stored in the following form,
# SHA1(salt + SHA1(password))
#
# $ mysql -u root -p
# mysql> use bitnami_redmine;
# Database changed
# mysql> select * from users
[List.Generic:dynamic_1501]
Expression=sha1($s.sha1($p)) (Redmine)
Flag=MGF_INPUT_20_BYTE
Flag=MGF_SALTED
Flag=MGF_FLAT_BUFFERS
Flag=MGF_KEYS_BASE16_IN1_SHA1
SaltLen=32
MaxInputLenX86=110
MaxInputLen=110
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_input2_from_input
Func=DynamicFunc__SHA1_crypt_input2_to_output1_FINAL
Test=$dynamic_1501$dd49e260795cb71da6904b9bccec30cb79b189f5$21737e0ab18ae77caec21f73c6e60c8d:redminecrap
Test=$dynamic_1501$713769f2b8824e8f5abc2d3e4f9326f32ff1d46b$5bfe6f1c0f7a8d802032d1bf85225400:redminefff

# In XenForo, the hashed password is stored in the following form(s),
# sha1(sha1(password).salt)
#
# OR
#
# sha256(sha256(password).salt)
#   NOTE, added MGF_KEYS_BASE16_IN1_SHA1 and MGF_KEYS_BASE16_IN1_SHA256 flags
#   and the many salts speed is now greatly improved.
[List.Generic:dynamic_1502]
Expression=sha1(sha1($p).$s) (XenForo SHA-1)
Flag=MGF_INPUT_20_BYTE
Flag=MGF_SALTED
Flag=MGF_FLAT_BUFFERS
Flag=MGF_KEYS_BASE16_IN1_SHA1
SaltLen=-120  // dont know, so made it big, jfoug
MaxInputLenX86=110
MaxInputLen=110
Func=DynamicFunc__set_input_len_40
Func=DynamicFunc__append_salt
Func=DynamicFunc__SHA1_crypt_input1_to_output1_FINAL
Test=$dynamic_1502$fd74fa6521e515921ad843a8465e34b703960db1$dummysalt:password

# note this hash could use the pre-compute limb-1 optimization we are wanting to do.
# that would take it from 3 sha256 limbs to 1 sha256 limb (in many salts). Right now,
# we have reduced it from 3 limbs to 2 limbs (for many salts).
[List.Generic:dynamic_1503]
Expression=sha256(sha256($p).$s) (XenForo SHA-256)
Flag=MGF_INPUT_32_BYTE
Flag=MGF_SALTED
Flag=MGF_FLAT_BUFFERS
Flag=MGF_KEYS_BASE16_IN1_SHA256
MaxInputLenX86=110
SaltLen=-120  // dont know, so made it big, jfoug
MaxInputLen=110
Func=DynamicFunc__set_input_len_64
Func=DynamicFunc__append_salt
Func=DynamicFunc__SHA256_crypt_input1_to_output1_FINAL
Test=$dynamic_1503$453f2e21fa6c150670d3ecf0e4a0ff3bab8b1903c2e96ad655d960b95f104248$697de9eda4a02563a7ec66d42d4a96995cb2948e29ab76fbcc89e8db71dd10f1:password
Test=$dynamic_1503$a8a0e9545c1475e8546f8546d87fe2516cf525c12ad79a6a7a8fee2fb0d8afd3$697de9eda4a02563a7ec66d42d4a96995cb2948e29ab76fbcc89e8db71dd10f1:verlongcrappypassword01234567890

# http://wiki.insidepro.com/index.php/sha1($a.$p.$s)
[List.Generic:dynamic_1504]
Expression=sha1($s.$p.$s)
Flag=MGF_INPUT_20_BYTE
Flag=MGF_SALTED
Flag=MGF_FLAT_BUFFERS
MaxInputLenX86=110
MaxInputLen=110
SaltLen=-68  // dont know, so made it max size that fits in 4 limb buffer, jfoug
Func=DynamicFunc__clean_input_kwik
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_salt
Func=DynamicFunc__SHA1_crypt_input1_to_output1_FINAL
Test=$dynamic_1504$114e4978430ee4fe2bc492f059f5c7aa400bf2fe$Salt:abcd
Test=$dynamic_1504$aab04277ffba1dee47288b05fa58d25e49a1935e$Salt:12345678
Test=$dynamic_1504$3b71a92fb2f4aeda9ae38211b67c5a4dc2a1771a$Salt:

# md5($p.$s.md5($p.$s)) (saw it on https://hashcat.net/trac)
[List.Generic:dynamic_1505]
Expression=md5($p.$s.md5($p.$s))
# to make flat (allows much longer passwords and salts)
Flag=MGF_FLAT_BUFFERS
MaxInputLen=110
SaltLen=-64
Flag=MGF_SALTED
MaxInputLenX86=110
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_salt
Func=DynamicFunc__crypt_md5
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_keys2
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__MD5_crypt_input2_to_output1_FINAL
Test=$dynamic_1505$b8bbabb1eb9802a2e962de0207ca5172$aaaSXB:test1

# https://www.trustwave.com/Resources/SpiderLabs-Blog/Changes-in-Oracle-Database-12c-password-hashes/
[List.Generic:dynamic_1506]
Expression=md5($u.:XDB:.$p) (Oracle 12c "H" hash)
Flag=MGF_USERNAME
CONST1=:XDB:
MaxInputLen=23
MaxInputLenX86=110
SaltLen=-27
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_userid
Func=DynamicFunc__append_input1_from_CONST1
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt
Test=$dynamic_1506$dc9894a01797d91d92eca1da66242209:epsilon:DEMO

# salt here is really a const.
[List.Generic:dynamic_1507]
Expression=sha1(utf16($const.$p)) (Mcafee master pass)
CONST1=\x01\x0f\x0d\x33
Flag=MGF_FLAT_BUFFERS
Flag=MGF_INPUT_20_BYTE
MaxInputLen=110
MaxInputLenX86=110
Func=DynamicFunc__clean_input
Func=DynamicFunc__setmode_unicode
Func=DynamicFunc__append_input1_from_CONST1
Func=DynamicFunc__append_keys
Func=DynamicFunc__SHA1_crypt_input1_to_output1_FINAL
Test=$dynamic_1507$d4eaf666d09316f9d61b14753353a73d5fbcf048:test
Test=$dynamic_1507$9dbe0d0ea16ae0a14c0c81a7c962b5a16e777259:test1

# Newer SunShop Shopping Cart. Older SunShop 4.1.0 uses md5($p) as the hashing
# scheme. It seems that both these hash types can live together in a single
# SunShop database.
[List.Generic:dynamic_1518]
Expression=md5(sha1($p).md5($p).sha1($p))
Flag=MGF_FLAT_BUFFERS
MaxInputLenX86=110
MaxInputLen=110
Func=DynamicFunc__clean_input_kwik
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_keys
Func=DynamicFunc__SHA1_crypt_input1_append_input2
Func=DynamicFunc__MD5_crypt_input1_append_input2
Func=DynamicFunc__SHA1_crypt_input1_append_input2
Func=DynamicFunc__MD5_crypt_input2_to_output1_FINAL
Test=$dynamic_1518$c756b56aed8d6748ee63e1e270c71a3f:password
Test=$dynamic_1518$8e6db6b58e9e326aba17e19a36c79d95:menura
Test=$dynamic_1518$2abe0f6794cc57663527ce7ab81fdaf3:stealth
Test=$dynamic_1518$08793c9ab17a586b3af71d28e1cae2c1:fletch
Test=$dynamic_1518$b19d46258f6a00f151367024789d71f1:smurfs
Test=$dynamic_1518$065c78f47a7da2e2ca2bd76eed10f6cd:ralphy1
Test=$dynamic_1518$82f7dd8a757d1a79126817940336087d:Kitesurfing1

# Telegram for Android hashes. Use ../run/telegram2john.py to extract the hashes.
[List.Generic:dynamic_1528]
Expression=sha256($s.$p.$s) (Telegram for Android)
Flag=MGF_INPUT_32_BYTE
Flag=MGF_SALTED
Flag=MGF_FLAT_BUFFERS
SaltLen=16
Func=DynamicFunc__clean_input_kwik
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_salt
Func=DynamicFunc__SHA256_crypt_input1_to_output1_FINAL
Test=$dynamic_1528$dab5552484cc327bd6d23b2a1ceb55b6ffb30f305bc09962a9102a6cec63773c$HEX$9533cd79bf8739bdd47ff8998aaf578c:1234
Test=$dynamic_1528$cad3fe1d4df2bf68c23f003e771c79fa42d10ae9a03671019d9c91a266a91372$HEX$901c3371d7de4b525b0e0a6abf4f392e:0987

# DeepSound hashes. Use ../run/deepsound2john.py to extract the hashes.
[List.Generic:dynamic_1529]
Expression=sha1($p null_padded_to_len_32) (DeepSound)
Flag=MGF_INPUT_20_BYTE
Flag=MGF_FLAT_BUFFERS
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_keys
Func=DynamicFunc__set_input_len_32
Func=DynamicFunc__SHA1_crypt_input1_to_output1_FINAL
Test=$dynamic_1529$6f9fa2285514c73bcac858496361f19f477ee416:deep5ound
Test=$dynamic_1529$66cad8923499423fa0c1d3974256d957840b9d69:iqlusion
Test=$dynamic_1529$a3eb15172cc7e6090a2eb32e6dc8c3bd30c39a02:abcdefghijklmnopqrstuvwxyz012345

# MONGODB-CR system hashes
# Input hash format => username:$dynamic_1550$hash
[List.Generic:dynamic_1550]
Expression=md5($u.:mongo:.$p) (MONGODB-CR system hash)
Flag=MGF_USERNAME
CONST1=:mongo:
MaxInputLen=23
MaxInputLenX86=110
# note, saltlen + length(:mongo:) + length(plain) must stay <= 55 for SIMD
#   so 25+7+23 == 55
SaltLen=-25
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_userid
Func=DynamicFunc__append_input1_from_CONST1
Func=DynamicFunc__append_keys
Func=DynamicFunc__crypt
Test=$dynamic_1550$08f32db65f837a52cd791bd923a61e95$$Usomeadmin:secret
Test=$dynamic_1550$819951ad797c3564148a77cbecf3b166$$Uadmin:secret@12345

# MONGODB-CR network hashes  (user name < 8 bytes long)
# Input hash format => username:$dynamic_1551$hash$salt$$Uusername
[List.Generic:dynamic_1551]
Expression=md5($s.$u.(md5($u.:mongo:.$p)) (MONGODB-CR network hash)
Flag=MGF_USERNAME
CONST1=:mongo:
MaxInputLen=23
MaxInputLenX86=110
# note, saltlen + length(:mongo:) + length(plain) must stay <= 55 for SIMD
#   so 25+7+23 == 55
SaltLen=16
Func=DynamicFunc__clean_input
Func=DynamicFunc__clean_input2
Func=DynamicFunc__append_userid
Func=DynamicFunc__append_input1_from_CONST1
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_userid2
Func=DynamicFunc__crypt
Func=DynamicFunc__append_from_last_output_to_input2_as_base16
Func=DynamicFunc__crypt_md5_in2_to_out1
Test=$dynamic_1551$0c85e3f74adce5d037426791940c820a$58d3229c83e3f87e$$Usa:sa
Test=$dynamic_1551$797d7e18879446845f10ae9d519960b2$10441db416a99ffc$$Usa:longpassword
Test=$dynamic_1551$a5ca2c517c06fdfb773144d53fb26f56$9b90cf265f3194d7$$UHerman:123456789
Test=$dynamic_1551$441d6ece7356c67dcc69dd26e7e0501f$be8fa52f0e64c250$$Usz110:passWOrd
Test=$dynamic_1551$c95e106f1d9952c88044a0b21a6bd3fd$304b81adddfb4d6f$$Ujack:

# MONGODB-CR network hashes  (user name >= 8 bytes long)
# Input hash format => username:$dynamic_1552$hash$salt$$Uusername
[List.Generic:dynamic_1552]
Expression=md5($s.$u.(md5($u.:mongo:.$p)) (MONGODB-CR network hash)
Flag=MGF_USERNAME
Flag=MGF_FLAT_BUFFERS
CONST1=:mongo:
MaxInputLen=110
MaxInputLenX86=110
SaltLen=16
Func=DynamicFunc__clean_input_kwik
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_userid
Func=DynamicFunc__append_input1_from_CONST1
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_userid2
Func=DynamicFunc__MD5_crypt_input1_append_input2
Func=DynamicFunc__MD5_crypt_input2_to_output1_FINAL
Test=$dynamic_1552$10290925d16d81e50db242c9f3572d91$0000000000000000$$Ulongusername:longpassword@12345678
Test=$dynamic_1552$53257e018399a241849cb04c70ba8daa$0000000000000000$$Ulongusername:longpassword
Test=$dynamic_1552$1abe48bac6ad0bf567ab51b094f026a9$86336266301fb552$$Ulongusername:longpassword
Test=$dynamic_1552$5c414259f7f7a42f8c4d1b6ffb37913a$8c82aec197929775$$Ueight18_characters:123

# SocialEngine hashes (Elijah [W&P])
#
# hash = md5('core secret'.'password'.'salt')
# core.secret -> MySQL 'engine4_core_settings' table, row 'core.secret'
# salt -> MySQL 'engine4_users' table, 'salt' column
[List.Generic:dynamic_1560]
Expression=md5($s.$p.$s2) (SocialEngine)
Flag=MGF_SALTED
Flag=MGF_SALTED2
Flag=MGF_FLAT_BUFFERS
SaltLen=-46
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_keys
Func=DynamicFunc__append_2nd_salt
Func=DynamicFunc__MD5_crypt_input1_to_output1_FINAL
Test=$dynamic_1560$55fce7789372d510023fc819c0ce55a6$a6ebe407fa6e2337cb2deb573d17791e$$21060744:test1
Test=$dynamic_1560$fd880f2c10f148c409f3c850a52201b0$6cbe843e024f59827c55f3a32d1c3be9$$22262250:thatsworking
Test=$dynamic_1560$2b199a07acf8e9e36e47ec2a0178933b$2a4c7cf421315f49fae230e80acfa218$$29597016:test3
Test=$dynamic_1560$13d806a7e87bc1b551478742349882a9$2161869cadcb41f1cc1e939f191c0bb35e58a9a7$$21060744:123123

# <AUTHOR> <EMAIL>)
# Hash is password variable from ./lib/password.properties
# Salt is admin.userid.root.salt variable from ./lib/neo-security.xml
[List.Generic:dynamic_1588]
Expression=sha256($s.sha1($p)) (ColdFusion 11)
Flag=MGF_INPUT_32_BYTE
Flag=MGF_SALTED
Flag=MGF_FLAT_BUFFERS
Flag=MGF_BASE_16_OUTPUT_UPCASE
SaltLen=64
Func=DynamicFunc__clean_input_kwik
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_keys2
Func=DynamicFunc__SHA1_crypt_input2_append_input1
Func=DynamicFunc__SHA256_crypt_input1_to_output1_FINAL
Test=$dynamic_1588$37F816D599BFD69C5A0D750198AB6E46E26CEB120C9AF3B1E5306515058CBAE8$D7B6D57262290BC0A634D2D1A0DFE59F1FBE47885DBC9BB1CEBA8EA9D09D9839:test1234

# IBM AS/400 SHA1 hashes   !NOTE, salt is pre prepared, utf16be(space_pad_10(uc($user_name))
[List.Generic:dynamic_1590]
Expression=sha1(utf16be(space_pad_10(uc($s)).$p)) (IBM AS/400 SHA1)
Flag=MGF_INPUT_20_BYTE
Flag=MGF_SALTED
Flag=MGF_FLAT_BUFFERS
Flag=MGF_BASE_16_OUTPUT_UPCASE
Flag=MGF_UTF8
SaltLen=20
Func=DynamicFunc__clean_input_kwik
Func=DynamicFunc__append_salt
Func=DynamicFunc__setmode_unicodeBE
Func=DynamicFunc__append_keys
Func=DynamicFunc__SHA1_crypt_input1_to_output1_FINAL
Test=$dynamic_1590$4C106E52CA196986E1C52C7FCD02AF046B76C73C$HEX$0052004F00420020002000200020002000200020:banaan
Test=$dynamic_1590$CED8050C275A5005D101051FF5BCCADF693E8AB7$HEX$0042004100520054002000200020002000200020:Kulach007
Test=$dynamic_1590$1BA6C7D54E9696ED33F4DF201E348CA8CA815F75$HEX$005300590053004F005000520020002000200020:T0Psecret!
Test=$dynamic_1590$A1284B4F1BDD7ED598D4B5060D861D6D614620D3$HEX$00530059005300540045004D0020002000200020:P@ssword01
Test=$dynamic_1590$94C55BC7EDF1996AC62E8145CDBFA285CA79ED2E$HEX$0051005300590053004400420041002000200020:qsysdba
Test=$dynamic_1590$CDF4063E283B51EDB7B9A8E6E542042000BD9AE9$HEX$0051005300450043004F00460052002000200020:qsecofr!
Test=$dynamic_1590$44D43148CFE5CC3372AFD2610BEE3D226B2B50C5$HEX$0054004500530054003100200020002000200020:password1
Test=$dynamic_1590$349B12D6588843A1632649A501ABC353EBF409E4$HEX$0054004500530054003200200020002000200020:secret
Test=$dynamic_1590$A97F2F9ED9977A8A628F8727E2851415B06DC540$HEX$0054004500530054003300200020002000200020:test3

# wbb3 SHA1 hashes
[List.Generic:dynamic_1592]
Expression=sha1($s.sha1($s.sha1($p))) (wbb3)
Flag=MGF_INPUT_20_BYTE
Flag=MGF_SALTED
Flag=MGF_FLAT_BUFFERS
Flag=MGF_KEYS_BASE16_IN1_SHA1
Flag=MGF_FULL_CLEAN_REQUIRED2
SaltLen=40
Func=DynamicFunc__clean_input2_kwik
Func=DynamicFunc__append_salt2
Func=DynamicFunc__append_input2_from_input
Func=DynamicFunc__LargeHash_set_offset_40
Func=DynamicFunc__SHA1_crypt_input2_at_offset_input2
Func=DynamicFunc__SHA1_crypt_input2_to_output1_FINAL
Test=$dynamic_1592$e2063f7c629d852302d3020599376016ff340399$0b053db07dc02bc6f6e24e00462f17e3c550afa9:123456
Test=$dynamic_1592$f6975cc560c5d03feb702158d08f90bf2fa773d6$0b053db07dc02bc6f6e24e00462f17e3c550afa9:password
Test=$dynamic_1592$2c56d23b44eb122bb176dfa2a1452afaf89f1143$a710463f75bf4568d398db32a53f9803007388a3:123456
Test=$dynamic_1592$2596b5f8e7cdaf4b15604ad336b810e8e2935b1d$1039145e9e785ddb2ac7ccca89ac1b159b595cc1:12345678
Test=$dynamic_1592$26496a87c1a7dd68f7beceb2fc40b6fc4223a453$db763342e23f8ccdbd9c90d1cc7896d80b7e0a44:12345678
Test=$dynamic_1592$d945c02cf85738b7db4f4f05edd676283280a513$bf2c7d0c8fb6cb146adf8933e32da012d31b5bbb:123456789
Test=$dynamic_1592$e3e03fe02223c5030e834f81997f614b43441853$d132b22d3f1d942b99cc1f5fbd5cc3eb0824d608:1234567890

# All credit for this format goes to Alexey Tyurin (ERPScan), François Gaudreault, and Martin Lemay
# http://gosecure.net/2016/05/04/oracle-peoplesoft-still-a-threat-for-enterprises/ (source)
# https://erpscan.com/press-center/blog/peoplesoft-security-part-4-peoplesoft-pentest-using-tokenchpoken-tool/
# https://erpscan.com/wp-content/uploads/tools/ERPScan-tockenchpoken.zip
[List.Generic:dynamic_1600]
Expression=sha1($s.utf16le($p)) (Oracle PeopleSoft PS_TOKEN)
Flag=MGF_INPUT_20_BYTE
Flag=MGF_FLAT_BUFFERS
Flag=MGF_SALTED
Flag=MGF_UTF8
SaltLen=-150
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_salt
Func=DynamicFunc__setmode_unicode
Func=DynamicFunc__append_keys
Func=DynamicFunc__SHA1_crypt_input1_to_output1_FINAL
Test=$dynamic_1600$e6155f87b073451076d81e3505f8b9fcd3f53b5a$HEX$710000000403020101000000bc0200000000000010500050005700450042004500580054000645004e0047000e50005300460054005f00480052003432003000310036002d00300034002d00300038002d00310039002e00320037002e00300035002e0030003000300030003000320000:password
Test=$dynamic_1600$b5e335754127b25ba6f99a94c738e24cd634c35a$HEX$aa07d396f5038a6cbeded88d78d1d6c907e4079b3dc2e12fddee409a51cc05ae73e8cc24d518c923a2f79e49376594503e6238b806bfe33fa8516f4903a9b4:hashcat
Test=$dynamic_1600$ac869d82e768c1af0e2b80679ddee8efe769d480$HEX$650000000403020101000000bc0200000000000004500053000645004e0047000e50005300460054005f00480052003432003000310035002d00300037002d00300031002d00300038002e00300036002e00340036002e0039003900390035003400330000:password@12345

# https://github.com/neo-project/neo-gui (tested with Neo GUI v2.3.2)
[List.Generic:dynamic_1608]
Expression=sha256(sha256_raw(sha256_raw($p))) (Neo Wallet)
Flag=MGF_FLAT_BUFFERS
Flag=MGF_INPUT_32_BYTE
MaxInputLenX86=110
MaxInputLen=110
Func=DynamicFunc__clean_input_kwik
Func=DynamicFunc__LargeHash_OUTMode_raw
Func=DynamicFunc__append_keys
Func=DynamicFunc__SHA256_crypt_input1_overwrite_input2
Func=DynamicFunc__SHA256_crypt_input2_overwrite_input2
Func=DynamicFunc__SHA256_crypt_input2_to_output1_FINAL
Test=$dynamic_1608$f2a778f1a6ed3d5bc59a5d79104c598f3f07093f240ca4e91333fb09ed4f36da:abc
Test=$dynamic_1608$8b12147de49a2832aca47a5bf6fbca12689420ac14c2547ab90f6d495f21f6dc:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzABCDEF
Test=$dynamic_1608$2a1a9918abe22f14d737462301e0c17b125a5f9ba11dc1e872b5320180437d12:openwall

# https://www.oneidentity.com/products/authentication-services/
# Author: Tim Brown. Borrowed from https://github.com/portcullislabs/linikatz (under BSD 3-Clause "New" or "Revised" License).
[List.Generic:dynamic_1602]
Expression=sha256(#.$salt.-.$pass) (QAS vas_auth)
Flag=MGF_INPUT_32_BYTE
Flag=MGF_USERNAME
Flag=MGF_SALTED
Flag=MGF_FLAT_BUFFERS
CONST1=#
CONST2=-
SaltLen=36
Func=DynamicFunc__clean_input
Func=DynamicFunc__append_input1_from_CONST1
Func=DynamicFunc__append_salt
Func=DynamicFunc__append_input1_from_CONST2
Func=DynamicFunc__append_keys
Func=DynamicFunc__SHA256_crypt_input1_to_output1_FINAL
Test=$dynamic_1602$9b4d1328a3dc064704301d2da2975f97b9212d8f08539214b27fd3106dc208ff$C34208EA-8C33-473D-A9B4-53FB40347EA0:P0rtcu11i5!:<EMAIL>

# this should be last line of the file.  Put other formats before this.  The formats in
# the following included file are replacement formats for the MD4/5 formats which use
# 'intermixed' SSE for speed, BUT which can not process longer passwords, due to being
# limited to a single SSE buffer.  The formats in dynamic_flat_sse_formats.conf are using
# the large hash 'flat' methods, which allow multiple SSE buffers. They are slower (sometimes
# a LOT slower), than the intermixed SSE.  But they are much faster than oSSL code, and can
# take full length passwords (110 bytes).

.include <dynamic_flat_sse_formats.conf>
