#!/usr/bin/env python3
"""
提取XML内容
"""

import struct
import os

def extract_xml_content():
    """提取XML内容"""
    filename = "Final Exam(MS).docx"
    
    print("提取XML内容...")
    
    # 位置16显示了XML片段，让我们提取更多数据
    base_size = 31
    base_sector = 30
    step = 32
    
    with open(filename, 'rb') as f:
        f.seek(0, 2)
        file_size = f.tell()
        
        # 从位置16开始提取更多数据
        start_position = 16
        xml_data = b""
        
        for i in range(start_position, 100):  # 检查更多位置
            size = base_size + (i-1) * step
            sector = base_sector + (i-1) * step
            file_offset = 512 + sector * 512
            
            if file_offset >= file_size:
                break
                
            f.seek(file_offset)
            data = f.read(size)
            
            if data:
                xml_data += data
                
                # 检查是否包含XML内容
                try:
                    text = data.decode('utf-8', errors='ignore')
                    if any(xml_tag in text for xml_tag in ['<', '>', 'xml', 'name=', 'xs:']):
                        print(f"位置{i}: 发现XML内容")
                        print(f"  大小: {size} 字节")
                        print(f"  内容预览: {text[:100]}")
                        print()
                except:
                    pass
        
        # 保存XML数据
        with open("extracted_xml.bin", "wb") as f:
            f.write(xml_data)
        
        print(f"总共提取了 {len(xml_data)} 字节XML数据")
        
        # 尝试解码为文本
        try:
            xml_text = xml_data.decode('utf-8', errors='ignore')
            
            # 保存为文本文件
            with open("extracted_content.txt", "w", encoding='utf-8') as f:
                f.write(xml_text)
            
            print("已保存到: extracted_content.txt")
            
            # 显示前1000字符
            print("\n提取的内容 (前1000字符):")
            print("="*60)
            print(xml_text[:1000])
            print("="*60)
            
            # 查找特定的XML标签和内容
            print("\n查找重要信息:")
            
            # 查找可能的密码或关键信息
            keywords = ['password', 'key', 'secret', 'answer', 'solution', 'flag', 'content']
            for keyword in keywords:
                if keyword.lower() in xml_text.lower():
                    print(f"发现关键词: {keyword}")
                    
                    # 查找包含关键词的行
                    lines = xml_text.split('\n')
                    for i, line in enumerate(lines):
                        if keyword.lower() in line.lower():
                            print(f"  行{i+1}: {line.strip()}")
            
            # 查找XML元素
            import re
            
            # 查找所有XML标签
            tags = re.findall(r'<[^>]+>', xml_text)
            if tags:
                print(f"\n发现 {len(tags)} 个XML标签:")
                unique_tags = list(set(tags))[:20]  # 显示前20个唯一标签
                for tag in unique_tags:
                    print(f"  {tag}")
            
            # 查找属性值
            attributes = re.findall(r'(\w+)="([^"]*)"', xml_text)
            if attributes:
                print(f"\n发现 {len(attributes)} 个属性:")
                for attr, value in attributes[:20]:  # 显示前20个
                    print(f"  {attr} = \"{value}\"")
                    
        except Exception as e:
            print(f"解码失败: {e}")

def search_for_readable_content():
    """搜索可读内容"""
    print("\n" + "="*60)
    print("搜索所有可读内容...")
    
    try:
        with open("extracted_content.txt", "r", encoding='utf-8') as f:
            content = f.read()
        
        # 查找连续的可读文本块
        import re
        
        # 查找长度超过10个字符的可读文本
        readable_blocks = re.findall(r'[a-zA-Z0-9\s.,!?;:(){}[\]"\'=-]{10,}', content)
        
        print(f"发现 {len(readable_blocks)} 个可读文本块:")
        
        for i, block in enumerate(readable_blocks[:10]):  # 显示前10个
            clean_block = ' '.join(block.split())  # 清理空白字符
            if len(clean_block) > 20:
                print(f"\n文本块 {i+1}:")
                print(f"  {clean_block[:200]}...")
        
        # 查找可能的答案或内容
        print("\n查找可能的答案:")
        
        # 分割成行并查找有意义的内容
        lines = content.split('\n')
        meaningful_lines = []
        
        for line in lines:
            clean_line = line.strip()
            if len(clean_line) > 10 and any(c.isalpha() for c in clean_line):
                meaningful_lines.append(clean_line)
        
        print(f"发现 {len(meaningful_lines)} 行有意义的内容:")
        for i, line in enumerate(meaningful_lines[:20]):  # 显示前20行
            print(f"  {i+1:2d}: {line[:100]}")
            
    except Exception as e:
        print(f"搜索失败: {e}")

def main():
    extract_xml_content()
    search_for_readable_content()

if __name__ == "__main__":
    main()
