#!/usr/bin/env python3
"""
简化的密码破解脚本，不依赖额外库
"""

import hashlib
import os

def try_direct_decryption():
    """尝试直接解密文档"""
    print("尝试直接解密文档...")
    
    # 读取密码字典
    try:
        with open("wordlist.txt", "r", encoding='utf-8') as f:
            passwords = [line.strip() for line in f if line.strip()]
    except:
        print("无法读取wordlist.txt文件")
        return None
    
    filename = "Final Exam(MS).docx"
    
    # 尝试使用msoffcrypto直接解密
    try:
        import msoffcrypto
        import io
        
        print(f"尝试 {len(passwords)} 个密码...")
        
        for i, password in enumerate(passwords):
            try:
                if i % 20 == 0:
                    print(f"进度: {i}/{len(passwords)} - 当前密码: {password}")
                
                with open(filename, "rb") as f:
                    office_file = msoffcrypto.OfficeFile(f)
                    office_file.load_key(password=password)
                    
                    decrypted = io.BytesIO()
                    office_file.decrypt(decrypted)
                    
                    # 成功解密
                    output_filename = f"decrypted_final_{password.replace('/', '_').replace(':', '_').replace('-', '_')}.docx"
                    with open(output_filename, "wb") as out:
                        out.write(decrypted.getvalue())
                    
                    print(f"\n🎉 成功解密！密码是: {password}")
                    print(f"解密文件保存为: {output_filename}")
                    
                    # 分析解密后的文件
                    analyze_decrypted_file(output_filename)
                    
                    return password, output_filename
                    
            except Exception as e:
                if "Invalid password" not in str(e) and "could not be decrypted" not in str(e):
                    if i < 10:  # 只显示前10个错误
                        print(f"  密码 '{password}' 错误: {e}")
                continue
        
        print("所有密码都失败了")
        return None, None
        
    except ImportError:
        print("msoffcrypto模块不可用")
        return None, None

def analyze_decrypted_file(filename):
    """分析解密后的文件"""
    if not filename or not os.path.exists(filename):
        return
    
    print(f"\n分析解密文件: {filename}")
    
    try:
        import zipfile
        
        with zipfile.ZipFile(filename, 'r') as docx:
            print("文档结构:")
            for file_info in docx.filelist:
                print(f"  {file_info.filename}")
            
            # 读取主文档内容
            if 'word/document.xml' in docx.namelist():
                with docx.open('word/document.xml') as doc_xml:
                    content = doc_xml.read().decode('utf-8')
                    
                    # 保存XML内容
                    with open("document_content.xml", "w", encoding='utf-8') as f:
                        f.write(content)
                    print("XML内容已保存到: document_content.xml")
                    
                    # 提取纯文本
                    import re
                    
                    # 查找文本内容
                    text_matches = re.findall(r'<w:t[^>]*>([^<]+)</w:t>', content)
                    
                    if text_matches:
                        full_text = ' '.join(text_matches)
                        print(f"\n📄 文档内容:")
                        print("=" * 60)
                        print(full_text)
                        print("=" * 60)
                        
                        # 保存文本内容
                        with open("extracted_text.txt", "w", encoding='utf-8') as f:
                            f.write(full_text)
                        print("\n文本内容已保存到: extracted_text.txt")
                    else:
                        print("未找到文本内容")
                        
                        # 显示XML的前500字符
                        print(f"\nXML内容预览:")
                        print(content[:500])
                        
    except Exception as e:
        print(f"分析文件失败: {e}")

def generate_more_passwords():
    """生成更多密码候选"""
    print("生成更多密码候选...")
    
    base_passwords = ["a-dTJO2"]
    
    # 基于我们的分析生成更多密码
    new_passwords = []
    
    for base in base_passwords:
        # 原始密码
        new_passwords.append(base)
        
        # 大小写变体
        new_passwords.append(base.upper())
        new_passwords.append(base.lower())
        new_passwords.append(base.capitalize())
        
        # 去除特殊字符
        clean = base.replace('-', '').replace('_', '').replace('.', '')
        new_passwords.append(clean)
        new_passwords.append(clean.upper())
        new_passwords.append(clean.lower())
        
        # 替换特殊字符
        new_passwords.append(base.replace('-', '_'))
        new_passwords.append(base.replace('-', '.'))
        new_passwords.append(base.replace('-', ''))
        new_passwords.append(base.replace('-', '0'))
        new_passwords.append(base.replace('-', '1'))
        
        # 反转
        new_passwords.append(base[::-1])
        new_passwords.append(base[::-1].upper())
        new_passwords.append(base[::-1].lower())
        
        # 添加数字
        for i in range(10):
            new_passwords.append(f"{base}{i}")
            new_passwords.append(f"{i}{base}")
            new_passwords.append(f"{clean}{i}")
            new_passwords.append(f"{i}{clean}")
        
        # 添加常见后缀
        suffixes = ['123', '!', '@', '#', '$', '2024', '2025', 'pass', 'word', 'key']
        for suffix in suffixes:
            new_passwords.append(f"{base}{suffix}")
            new_passwords.append(f"{clean}{suffix}")
        
        # 添加常见前缀
        prefixes = ['pass', 'pwd', 'key', 'final', 'exam', 'test']
        for prefix in prefixes:
            new_passwords.append(f"{prefix}{base}")
            new_passwords.append(f"{prefix}{clean}")
    
    # 基于数学模式的密码
    math_passwords = [
        "31", "63", "95", "127", "159", "191", "223", "255",
        "1f", "3f", "5f", "7f", "9f", "bf", "df", "ff",
        "31639527", "31-63-95-127", "31639527",
        "11111", "111111", "1111111", "11111111"
    ]
    
    new_passwords.extend(math_passwords)
    
    # 去重
    unique_passwords = list(set(new_passwords))
    
    print(f"生成了 {len(unique_passwords)} 个新密码")
    
    # 保存到文件
    with open("extended_wordlist.txt", "w", encoding='utf-8') as f:
        for pwd in unique_passwords:
            f.write(pwd + '\n')
    
    print("扩展密码列表已保存到: extended_wordlist.txt")
    
    return unique_passwords

def try_extended_passwords():
    """尝试扩展的密码列表"""
    print("\n尝试扩展的密码列表...")
    
    # 生成更多密码
    extended_passwords = generate_more_passwords()
    
    filename = "Final Exam(MS).docx"
    
    try:
        import msoffcrypto
        import io
        
        print(f"尝试 {len(extended_passwords)} 个扩展密码...")
        
        for i, password in enumerate(extended_passwords):
            try:
                if i % 10 == 0:
                    print(f"进度: {i}/{len(extended_passwords)} - 当前: {password}")
                
                with open(filename, "rb") as f:
                    office_file = msoffcrypto.OfficeFile(f)
                    office_file.load_key(password=password)
                    
                    decrypted = io.BytesIO()
                    office_file.decrypt(decrypted)
                    
                    # 成功解密
                    output_filename = f"decrypted_extended_{password.replace('/', '_').replace(':', '_').replace('-', '_')}.docx"
                    with open(output_filename, "wb") as out:
                        out.write(decrypted.getvalue())
                    
                    print(f"\n🎉 扩展密码破解成功！密码是: {password}")
                    print(f"解密文件保存为: {output_filename}")
                    
                    analyze_decrypted_file(output_filename)
                    
                    return password, output_filename
                    
            except Exception as e:
                continue
        
        print("扩展密码列表也失败了")
        return None, None
        
    except ImportError:
        print("msoffcrypto模块不可用")
        return None, None

def main():
    print("Office文档密码破解工具")
    print("=" * 50)
    
    # 方法1：使用原始密码列表
    password, decrypted_file = try_direct_decryption()
    
    if password:
        print(f"\n🎉 成功！密码是: {password}")
        return
    
    # 方法2：使用扩展密码列表
    password, decrypted_file = try_extended_passwords()
    
    if password:
        print(f"\n🎉 扩展破解成功！密码是: {password}")
        return
    
    print("\n❌ 所有方法都失败了")
    print("\n可能的原因:")
    print("1. 密码不在我们的候选列表中")
    print("2. 文档使用了更强的加密")
    print("3. 需要专业的密码破解工具")
    
    print("\n建议:")
    print("1. 尝试安装John the Ripper或Hashcat")
    print("2. 使用更大的密码字典")
    print("3. 检查是否有其他线索")

if __name__ == "__main__":
    main()
