Cracking Tezos keys with JtR Jumbo
==================================

1. Run tezos2john.py and provide it with the required data. Run tezos2john.py
   without any options to see the usage instructions.

E.g. $ ../run/tezos2john.py 'put guide flat machine express cave hello connect stay local spike ski romance express brass' '<EMAIL>' 'tz1eTjPtwYjdcBMStwVdEcwY2YE3th1bXyMR' > hashes

E.g. $ ../run/tezos2john.py 'monster crack glance favorite humble group bone grid clock bottom employ gold jelly fatigue tragic' '<EMAIL>' 'tz1Zgd3LHuryw6rBzsQKnBMVqu99KzWankj8' >> hashes

The passwords for these sample hashes are "4FGU8MpuCo" and "VPhvU2LgyJ" respectively.

2. Run john on the output of tezos2john.py script.

E.g. $ ../run/john hashes

3. Wait for the password(s) to get cracked.
