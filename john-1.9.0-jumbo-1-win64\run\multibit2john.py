#!/usr/bin/env python

# This software is Copyright (c) 2017, Dhiru <PERSON>hol<PERSON> <dhiru.kholia at gmail.com>
# and it is hereby released under GPL v2 license.
#
# Major parts are borrowed from the "btcrecover" program which is,
# Copyright (C) 2014-2016 <PERSON> and under GPL v2.
#
# See https://github.com/gurnec/btcrecover for details.
#
# References,
#
# https://github.com/gurnec/btcrecover/blob/master/btcrecover/btcrpass.py

import os
import sys
import base64
import binascii

# The protobuf parsing code below is borrowed from the btcrecover, and was
# initially automatically generated by Google's protoc utility.
#
# Generated by the protocol buffer compiler. DO NOT EDIT!
# source: wallet.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
try:
    from google.protobuf import descriptor as _descriptor
    from google.protobuf import message as _message
    from google.protobuf import reflection as _reflection
    from google.protobuf import symbol_database as _symbol_database
    from google.protobuf import descriptor_pb2
except ImportError:
    sys.stderr.write("Install the missing protobuf package, use 'pip install --user protobuf' command to do so.\n")
    sys.exit(-1)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()

DESCRIPTOR = _descriptor.FileDescriptor(
  name='wallet.proto',
  package='wallet',
  serialized_pb=_b('\n\x0cwallet.proto\x12\x06wallet\"A\n\x0bPeerAddress\x12\x12\n\nip_address\x18\x01 \x02(\x0c\x12\x0c\n\x04port\x18\x02 \x02(\r\x12\x10\n\x08services\x18\x03 \x02(\x04\"M\n\rEncryptedData\x12\x1d\n\x15initialisation_vector\x18\x01 \x02(\x0c\x12\x1d\n\x15\x65ncrypted_private_key\x18\x02 \x02(\x0c\"y\n\x10\x44\x65terministicKey\x12\x12\n\nchain_code\x18\x01 \x02(\x0c\x12\x0c\n\x04path\x18\x02 \x03(\r\x12\x16\n\x0eissued_subkeys\x18\x03 \x01(\r\x12\x16\n\x0elookahead_size\x18\x04 \x01(\r\x12\x13\n\x0bisFollowing\x18\x05 \x01(\x08\"\x9a\x03\n\x03Key\x12\x1e\n\x04type\x18\x01 \x02(\x0e\x32\x10.wallet.Key.Type\x12\x14\n\x0csecret_bytes\x18\x02 \x01(\x0c\x12-\n\x0e\x65ncrypted_data\x18\x06 \x01(\x0b\x32\x15.wallet.EncryptedData\x12\x12\n\npublic_key\x18\x03 \x01(\x0c\x12\r\n\x05label\x18\x04 \x01(\t\x12\x1a\n\x12\x63reation_timestamp\x18\x05 \x01(\x03\x12\x33\n\x11\x64\x65terministic_key\x18\x07 \x01(\x0b\x32\x18.wallet.DeterministicKey\x12\x1a\n\x12\x64\x65terministic_seed\x18\x08 \x01(\x0c\x12;\n\x1c\x65ncrypted_deterministic_seed\x18\t \x01(\x0b\x32\x15.wallet.EncryptedData\"a\n\x04Type\x12\x0c\n\x08ORIGINAL\x10\x01\x12\x18\n\x14\x45NCRYPTED_SCRYPT_AES\x10\x02\x12\x1a\n\x16\x44\x45TERMINISTIC_MNEMONIC\x10\x03\x12\x15\n\x11\x44\x45TERMINISTIC_KEY\x10\x04\"5\n\x06Script\x12\x0f\n\x07program\x18\x01 \x02(\x0c\x12\x1a\n\x12\x63reation_timestamp\x18\x02 \x02(\x03\"\x92\x01\n\x10TransactionInput\x12\"\n\x1atransaction_out_point_hash\x18\x01 \x02(\x0c\x12#\n\x1btransaction_out_point_index\x18\x02 \x02(\r\x12\x14\n\x0cscript_bytes\x18\x03 \x02(\x0c\x12\x10\n\x08sequence\x18\x04 \x01(\r\x12\r\n\x05value\x18\x05 \x01(\x03\"\x7f\n\x11TransactionOutput\x12\r\n\x05value\x18\x01 \x02(\x03\x12\x14\n\x0cscript_bytes\x18\x02 \x02(\x0c\x12!\n\x19spent_by_transaction_hash\x18\x03 \x01(\x0c\x12\"\n\x1aspent_by_transaction_index\x18\x04 \x01(\x05\"\x89\x03\n\x15TransactionConfidence\x12\x30\n\x04type\x18\x01 \x01(\x0e\x32\".wallet.TransactionConfidence.Type\x12\x1a\n\x12\x61ppeared_at_height\x18\x02 \x01(\x05\x12\x1e\n\x16overriding_transaction\x18\x03 \x01(\x0c\x12\r\n\x05\x64\x65pth\x18\x04 \x01(\x05\x12)\n\x0c\x62roadcast_by\x18\x06 \x03(\x0b\x32\x13.wallet.PeerAddress\x12\x34\n\x06source\x18\x07 \x01(\x0e\x32$.wallet.TransactionConfidence.Source\"O\n\x04Type\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0c\n\x08\x42UILDING\x10\x01\x12\x0b\n\x07PENDING\x10\x02\x12\x15\n\x11NOT_IN_BEST_CHAIN\x10\x03\x12\x08\n\x04\x44\x45\x41\x44\x10\x04\"A\n\x06Source\x12\x12\n\x0eSOURCE_UNKNOWN\x10\x00\x12\x12\n\x0eSOURCE_NETWORK\x10\x01\x12\x0f\n\x0bSOURCE_SELF\x10\x02\"\xb4\x05\n\x0bTransaction\x12\x0f\n\x07version\x18\x01 \x02(\x05\x12\x0c\n\x04hash\x18\x02 \x02(\x0c\x12&\n\x04pool\x18\x03 \x01(\x0e\x32\x18.wallet.Transaction.Pool\x12\x11\n\tlock_time\x18\x04 \x01(\r\x12\x12\n\nupdated_at\x18\x05 \x01(\x03\x12\x33\n\x11transaction_input\x18\x06 \x03(\x0b\x32\x18.wallet.TransactionInput\x12\x35\n\x12transaction_output\x18\x07 \x03(\x0b\x32\x19.wallet.TransactionOutput\x12\x12\n\nblock_hash\x18\x08 \x03(\x0c\x12 \n\x18\x62lock_relativity_offsets\x18\x0b \x03(\x05\x12\x31\n\nconfidence\x18\t \x01(\x0b\x32\x1d.wallet.TransactionConfidence\x12\x35\n\x07purpose\x18\n \x01(\x0e\x32\x1b.wallet.Transaction.Purpose:\x07UNKNOWN\x12+\n\rexchange_rate\x18\x0c \x01(\x0b\x32\x14.wallet.ExchangeRate\x12\x0c\n\x04memo\x18\r \x01(\t\"Y\n\x04Pool\x12\x0b\n\x07UNSPENT\x10\x04\x12\t\n\x05SPENT\x10\x05\x12\x0c\n\x08INACTIVE\x10\x02\x12\x08\n\x04\x44\x45\x41\x44\x10\n\x12\x0b\n\x07PENDING\x10\x10\x12\x14\n\x10PENDING_INACTIVE\x10\x12\"\x94\x01\n\x07Purpose\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x10\n\x0cUSER_PAYMENT\x10\x01\x12\x10\n\x0cKEY_ROTATION\x10\x02\x12\x1c\n\x18\x41SSURANCE_CONTRACT_CLAIM\x10\x03\x12\x1d\n\x19\x41SSURANCE_CONTRACT_PLEDGE\x10\x04\x12\x1b\n\x17\x41SSURANCE_CONTRACT_STUB\x10\x05\"N\n\x10ScryptParameters\x12\x0c\n\x04salt\x18\x01 \x02(\x0c\x12\x10\n\x01n\x18\x02 \x01(\x03:\x05\x31\x36\x33\x38\x34\x12\x0c\n\x01r\x18\x03 \x01(\x05:\x01\x38\x12\x0c\n\x01p\x18\x04 \x01(\x05:\x01\x31\"8\n\tExtension\x12\n\n\x02id\x18\x01 \x02(\t\x12\x0c\n\x04\x64\x61ta\x18\x02 \x02(\x0c\x12\x11\n\tmandatory\x18\x03 \x02(\x08\" \n\x03Tag\x12\x0b\n\x03tag\x18\x01 \x02(\t\x12\x0c\n\x04\x64\x61ta\x18\x02 \x02(\x0c\"5\n\x11TransactionSigner\x12\x12\n\nclass_name\x18\x01 \x02(\t\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\x0c\"\x89\x05\n\x06Wallet\x12\x1a\n\x12network_identifier\x18\x01 \x02(\t\x12\x1c\n\x14last_seen_block_hash\x18\x02 \x01(\x0c\x12\x1e\n\x16last_seen_block_height\x18\x0c \x01(\r\x12!\n\x19last_seen_block_time_secs\x18\x0e \x01(\x03\x12\x18\n\x03key\x18\x03 \x03(\x0b\x32\x0b.wallet.Key\x12(\n\x0btransaction\x18\x04 \x03(\x0b\x32\x13.wallet.Transaction\x12&\n\x0ewatched_script\x18\x0f \x03(\x0b\x32\x0e.wallet.Script\x12\x43\n\x0f\x65ncryption_type\x18\x05 \x01(\x0e\x32\x1d.wallet.Wallet.EncryptionType:\x0bUNENCRYPTED\x12\x37\n\x15\x65ncryption_parameters\x18\x06 \x01(\x0b\x32\x18.wallet.ScryptParameters\x12\x12\n\x07version\x18\x07 \x01(\x05:\x01\x31\x12$\n\textension\x18\n \x03(\x0b\x32\x11.wallet.Extension\x12\x13\n\x0b\x64\x65scription\x18\x0b \x01(\t\x12\x19\n\x11key_rotation_time\x18\r \x01(\x04\x12\x19\n\x04tags\x18\x10 \x03(\x0b\x32\x0b.wallet.Tag\x12\x36\n\x13transaction_signers\x18\x11 \x03(\x0b\x32\x19.wallet.TransactionSigner\x12\x1e\n\x13sigsRequiredToSpend\x18\x12 \x01(\r:\x01\x31\";\n\x0e\x45ncryptionType\x12\x0f\n\x0bUNENCRYPTED\x10\x01\x12\x18\n\x14\x45NCRYPTED_SCRYPT_AES\x10\x02\"R\n\x0c\x45xchangeRate\x12\x12\n\ncoin_value\x18\x01 \x02(\x03\x12\x12\n\nfiat_value\x18\x02 \x02(\x03\x12\x1a\n\x12\x66iat_currency_code\x18\x03 \x02(\tB\x1d\n\x13org.bitcoinj.walletB\x06Protos')
)
_sym_db.RegisterFileDescriptor(DESCRIPTOR)



_KEY_TYPE = _descriptor.EnumDescriptor(
  name='Type',
  full_name='wallet.Key.Type',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ORIGINAL', index=0, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ENCRYPTED_SCRYPT_AES', index=1, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DETERMINISTIC_MNEMONIC', index=2, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DETERMINISTIC_KEY', index=3, number=4,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=607,
  serialized_end=704,
)
_sym_db.RegisterEnumDescriptor(_KEY_TYPE)

_TRANSACTIONCONFIDENCE_TYPE = _descriptor.EnumDescriptor(
  name='Type',
  full_name='wallet.TransactionConfidence.Type',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BUILDING', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PENDING', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NOT_IN_BEST_CHAIN', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DEAD', index=4, number=4,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=1287,
  serialized_end=1366,
)
_sym_db.RegisterEnumDescriptor(_TRANSACTIONCONFIDENCE_TYPE)

_TRANSACTIONCONFIDENCE_SOURCE = _descriptor.EnumDescriptor(
  name='Source',
  full_name='wallet.TransactionConfidence.Source',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SOURCE_UNKNOWN', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SOURCE_NETWORK', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SOURCE_SELF', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=1368,
  serialized_end=1433,
)
_sym_db.RegisterEnumDescriptor(_TRANSACTIONCONFIDENCE_SOURCE)

_TRANSACTION_POOL = _descriptor.EnumDescriptor(
  name='Pool',
  full_name='wallet.Transaction.Pool',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNSPENT', index=0, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SPENT', index=1, number=5,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INACTIVE', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DEAD', index=3, number=10,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PENDING', index=4, number=16,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PENDING_INACTIVE', index=5, number=18,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=1888,
  serialized_end=1977,
)
_sym_db.RegisterEnumDescriptor(_TRANSACTION_POOL)

_TRANSACTION_PURPOSE = _descriptor.EnumDescriptor(
  name='Purpose',
  full_name='wallet.Transaction.Purpose',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='USER_PAYMENT', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='KEY_ROTATION', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ASSURANCE_CONTRACT_CLAIM', index=3, number=3,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ASSURANCE_CONTRACT_PLEDGE', index=4, number=4,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ASSURANCE_CONTRACT_STUB', index=5, number=5,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=1980,
  serialized_end=2128,
)
_sym_db.RegisterEnumDescriptor(_TRANSACTION_PURPOSE)

_WALLET_ENCRYPTIONTYPE = _descriptor.EnumDescriptor(
  name='EncryptionType',
  full_name='wallet.Wallet.EncryptionType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNENCRYPTED', index=0, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ENCRYPTED_SCRYPT_AES', index=1, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=2948,
  serialized_end=3007,
)
_sym_db.RegisterEnumDescriptor(_WALLET_ENCRYPTIONTYPE)


_PEERADDRESS = _descriptor.Descriptor(
  name='PeerAddress',
  full_name='wallet.PeerAddress',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ip_address', full_name='wallet.PeerAddress.ip_address', index=0,
      number=1, type=12, cpp_type=9, label=2,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='port', full_name='wallet.PeerAddress.port', index=1,
      number=2, type=13, cpp_type=3, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='services', full_name='wallet.PeerAddress.services', index=2,
      number=3, type=4, cpp_type=4, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=24,
  serialized_end=89,
)


_ENCRYPTEDDATA = _descriptor.Descriptor(
  name='EncryptedData',
  full_name='wallet.EncryptedData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='initialisation_vector', full_name='wallet.EncryptedData.initialisation_vector', index=0,
      number=1, type=12, cpp_type=9, label=2,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='encrypted_private_key', full_name='wallet.EncryptedData.encrypted_private_key', index=1,
      number=2, type=12, cpp_type=9, label=2,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=91,
  serialized_end=168,
)


_DETERMINISTICKEY = _descriptor.Descriptor(
  name='DeterministicKey',
  full_name='wallet.DeterministicKey',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='chain_code', full_name='wallet.DeterministicKey.chain_code', index=0,
      number=1, type=12, cpp_type=9, label=2,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='path', full_name='wallet.DeterministicKey.path', index=1,
      number=2, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='issued_subkeys', full_name='wallet.DeterministicKey.issued_subkeys', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='lookahead_size', full_name='wallet.DeterministicKey.lookahead_size', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='isFollowing', full_name='wallet.DeterministicKey.isFollowing', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=170,
  serialized_end=291,
)


_KEY = _descriptor.Descriptor(
  name='Key',
  full_name='wallet.Key',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='wallet.Key.type', index=0,
      number=1, type=14, cpp_type=8, label=2,
      has_default_value=False, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='secret_bytes', full_name='wallet.Key.secret_bytes', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='encrypted_data', full_name='wallet.Key.encrypted_data', index=2,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='public_key', full_name='wallet.Key.public_key', index=3,
      number=3, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='label', full_name='wallet.Key.label', index=4,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='creation_timestamp', full_name='wallet.Key.creation_timestamp', index=5,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='deterministic_key', full_name='wallet.Key.deterministic_key', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='deterministic_seed', full_name='wallet.Key.deterministic_seed', index=7,
      number=8, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='encrypted_deterministic_seed', full_name='wallet.Key.encrypted_deterministic_seed', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _KEY_TYPE,
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=294,
  serialized_end=704,
)


_SCRIPT = _descriptor.Descriptor(
  name='Script',
  full_name='wallet.Script',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='program', full_name='wallet.Script.program', index=0,
      number=1, type=12, cpp_type=9, label=2,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='creation_timestamp', full_name='wallet.Script.creation_timestamp', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=706,
  serialized_end=759,
)


_TRANSACTIONINPUT = _descriptor.Descriptor(
  name='TransactionInput',
  full_name='wallet.TransactionInput',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transaction_out_point_hash', full_name='wallet.TransactionInput.transaction_out_point_hash', index=0,
      number=1, type=12, cpp_type=9, label=2,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='transaction_out_point_index', full_name='wallet.TransactionInput.transaction_out_point_index', index=1,
      number=2, type=13, cpp_type=3, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='script_bytes', full_name='wallet.TransactionInput.script_bytes', index=2,
      number=3, type=12, cpp_type=9, label=2,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sequence', full_name='wallet.TransactionInput.sequence', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='value', full_name='wallet.TransactionInput.value', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=762,
  serialized_end=908,
)


_TRANSACTIONOUTPUT = _descriptor.Descriptor(
  name='TransactionOutput',
  full_name='wallet.TransactionOutput',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='wallet.TransactionOutput.value', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='script_bytes', full_name='wallet.TransactionOutput.script_bytes', index=1,
      number=2, type=12, cpp_type=9, label=2,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='spent_by_transaction_hash', full_name='wallet.TransactionOutput.spent_by_transaction_hash', index=2,
      number=3, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='spent_by_transaction_index', full_name='wallet.TransactionOutput.spent_by_transaction_index', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=910,
  serialized_end=1037,
)


_TRANSACTIONCONFIDENCE = _descriptor.Descriptor(
  name='TransactionConfidence',
  full_name='wallet.TransactionConfidence',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='wallet.TransactionConfidence.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='appeared_at_height', full_name='wallet.TransactionConfidence.appeared_at_height', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='overriding_transaction', full_name='wallet.TransactionConfidence.overriding_transaction', index=2,
      number=3, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='depth', full_name='wallet.TransactionConfidence.depth', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='broadcast_by', full_name='wallet.TransactionConfidence.broadcast_by', index=4,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='source', full_name='wallet.TransactionConfidence.source', index=5,
      number=7, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _TRANSACTIONCONFIDENCE_TYPE,
    _TRANSACTIONCONFIDENCE_SOURCE,
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1040,
  serialized_end=1433,
)


_TRANSACTION = _descriptor.Descriptor(
  name='Transaction',
  full_name='wallet.Transaction',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='version', full_name='wallet.Transaction.version', index=0,
      number=1, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='hash', full_name='wallet.Transaction.hash', index=1,
      number=2, type=12, cpp_type=9, label=2,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='pool', full_name='wallet.Transaction.pool', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=4,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='lock_time', full_name='wallet.Transaction.lock_time', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='wallet.Transaction.updated_at', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='transaction_input', full_name='wallet.Transaction.transaction_input', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='transaction_output', full_name='wallet.Transaction.transaction_output', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='block_hash', full_name='wallet.Transaction.block_hash', index=7,
      number=8, type=12, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='block_relativity_offsets', full_name='wallet.Transaction.block_relativity_offsets', index=8,
      number=11, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='confidence', full_name='wallet.Transaction.confidence', index=9,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='purpose', full_name='wallet.Transaction.purpose', index=10,
      number=10, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='exchange_rate', full_name='wallet.Transaction.exchange_rate', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='memo', full_name='wallet.Transaction.memo', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _TRANSACTION_POOL,
    _TRANSACTION_PURPOSE,
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1436,
  serialized_end=2128,
)


_SCRYPTPARAMETERS = _descriptor.Descriptor(
  name='ScryptParameters',
  full_name='wallet.ScryptParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='salt', full_name='wallet.ScryptParameters.salt', index=0,
      number=1, type=12, cpp_type=9, label=2,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='n', full_name='wallet.ScryptParameters.n', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=True, default_value=16384,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='r', full_name='wallet.ScryptParameters.r', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=True, default_value=8,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='p', full_name='wallet.ScryptParameters.p', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=True, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2130,
  serialized_end=2208,
)


_EXTENSION = _descriptor.Descriptor(
  name='Extension',
  full_name='wallet.Extension',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='wallet.Extension.id', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='data', full_name='wallet.Extension.data', index=1,
      number=2, type=12, cpp_type=9, label=2,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='mandatory', full_name='wallet.Extension.mandatory', index=2,
      number=3, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2210,
  serialized_end=2266,
)


_TAG = _descriptor.Descriptor(
  name='Tag',
  full_name='wallet.Tag',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag', full_name='wallet.Tag.tag', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='data', full_name='wallet.Tag.data', index=1,
      number=2, type=12, cpp_type=9, label=2,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2268,
  serialized_end=2300,
)


_TRANSACTIONSIGNER = _descriptor.Descriptor(
  name='TransactionSigner',
  full_name='wallet.TransactionSigner',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='class_name', full_name='wallet.TransactionSigner.class_name', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='data', full_name='wallet.TransactionSigner.data', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2302,
  serialized_end=2355,
)


_WALLET = _descriptor.Descriptor(
  name='Wallet',
  full_name='wallet.Wallet',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='network_identifier', full_name='wallet.Wallet.network_identifier', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='last_seen_block_hash', full_name='wallet.Wallet.last_seen_block_hash', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='last_seen_block_height', full_name='wallet.Wallet.last_seen_block_height', index=2,
      number=12, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='last_seen_block_time_secs', full_name='wallet.Wallet.last_seen_block_time_secs', index=3,
      number=14, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='key', full_name='wallet.Wallet.key', index=4,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='transaction', full_name='wallet.Wallet.transaction', index=5,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='watched_script', full_name='wallet.Wallet.watched_script', index=6,
      number=15, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='encryption_type', full_name='wallet.Wallet.encryption_type', index=7,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='encryption_parameters', full_name='wallet.Wallet.encryption_parameters', index=8,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='version', full_name='wallet.Wallet.version', index=9,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=True, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='extension', full_name='wallet.Wallet.extension', index=10,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='description', full_name='wallet.Wallet.description', index=11,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='key_rotation_time', full_name='wallet.Wallet.key_rotation_time', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='tags', full_name='wallet.Wallet.tags', index=13,
      number=16, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='transaction_signers', full_name='wallet.Wallet.transaction_signers', index=14,
      number=17, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sigsRequiredToSpend', full_name='wallet.Wallet.sigsRequiredToSpend', index=15,
      number=18, type=13, cpp_type=3, label=1,
      has_default_value=True, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _WALLET_ENCRYPTIONTYPE,
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2358,
  serialized_end=3007,
)


_EXCHANGERATE = _descriptor.Descriptor(
  name='ExchangeRate',
  full_name='wallet.ExchangeRate',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='coin_value', full_name='wallet.ExchangeRate.coin_value', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='fiat_value', full_name='wallet.ExchangeRate.fiat_value', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='fiat_currency_code', full_name='wallet.ExchangeRate.fiat_currency_code', index=2,
      number=3, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3009,
  serialized_end=3091,
)

_KEY.fields_by_name['type'].enum_type = _KEY_TYPE
_KEY.fields_by_name['encrypted_data'].message_type = _ENCRYPTEDDATA
_KEY.fields_by_name['deterministic_key'].message_type = _DETERMINISTICKEY
_KEY.fields_by_name['encrypted_deterministic_seed'].message_type = _ENCRYPTEDDATA
_KEY_TYPE.containing_type = _KEY
_TRANSACTIONCONFIDENCE.fields_by_name['type'].enum_type = _TRANSACTIONCONFIDENCE_TYPE
_TRANSACTIONCONFIDENCE.fields_by_name['broadcast_by'].message_type = _PEERADDRESS
_TRANSACTIONCONFIDENCE.fields_by_name['source'].enum_type = _TRANSACTIONCONFIDENCE_SOURCE
_TRANSACTIONCONFIDENCE_TYPE.containing_type = _TRANSACTIONCONFIDENCE
_TRANSACTIONCONFIDENCE_SOURCE.containing_type = _TRANSACTIONCONFIDENCE
_TRANSACTION.fields_by_name['pool'].enum_type = _TRANSACTION_POOL
_TRANSACTION.fields_by_name['transaction_input'].message_type = _TRANSACTIONINPUT
_TRANSACTION.fields_by_name['transaction_output'].message_type = _TRANSACTIONOUTPUT
_TRANSACTION.fields_by_name['confidence'].message_type = _TRANSACTIONCONFIDENCE
_TRANSACTION.fields_by_name['purpose'].enum_type = _TRANSACTION_PURPOSE
_TRANSACTION.fields_by_name['exchange_rate'].message_type = _EXCHANGERATE
_TRANSACTION_POOL.containing_type = _TRANSACTION
_TRANSACTION_PURPOSE.containing_type = _TRANSACTION
_WALLET.fields_by_name['key'].message_type = _KEY
_WALLET.fields_by_name['transaction'].message_type = _TRANSACTION
_WALLET.fields_by_name['watched_script'].message_type = _SCRIPT
_WALLET.fields_by_name['encryption_type'].enum_type = _WALLET_ENCRYPTIONTYPE
_WALLET.fields_by_name['encryption_parameters'].message_type = _SCRYPTPARAMETERS
_WALLET.fields_by_name['extension'].message_type = _EXTENSION
_WALLET.fields_by_name['tags'].message_type = _TAG
_WALLET.fields_by_name['transaction_signers'].message_type = _TRANSACTIONSIGNER
_WALLET_ENCRYPTIONTYPE.containing_type = _WALLET
DESCRIPTOR.message_types_by_name['PeerAddress'] = _PEERADDRESS
DESCRIPTOR.message_types_by_name['EncryptedData'] = _ENCRYPTEDDATA
DESCRIPTOR.message_types_by_name['DeterministicKey'] = _DETERMINISTICKEY
DESCRIPTOR.message_types_by_name['Key'] = _KEY
DESCRIPTOR.message_types_by_name['Script'] = _SCRIPT
DESCRIPTOR.message_types_by_name['TransactionInput'] = _TRANSACTIONINPUT
DESCRIPTOR.message_types_by_name['TransactionOutput'] = _TRANSACTIONOUTPUT
DESCRIPTOR.message_types_by_name['TransactionConfidence'] = _TRANSACTIONCONFIDENCE
DESCRIPTOR.message_types_by_name['Transaction'] = _TRANSACTION
DESCRIPTOR.message_types_by_name['ScryptParameters'] = _SCRYPTPARAMETERS
DESCRIPTOR.message_types_by_name['Extension'] = _EXTENSION
DESCRIPTOR.message_types_by_name['Tag'] = _TAG
DESCRIPTOR.message_types_by_name['TransactionSigner'] = _TRANSACTIONSIGNER
DESCRIPTOR.message_types_by_name['Wallet'] = _WALLET
DESCRIPTOR.message_types_by_name['ExchangeRate'] = _EXCHANGERATE

PeerAddress = _reflection.GeneratedProtocolMessageType('PeerAddress', (_message.Message,), dict(
  DESCRIPTOR = _PEERADDRESS,
  __module__ = 'wallet_pb2'
  # @@protoc_insertion_point(class_scope:wallet.PeerAddress)
  ))
_sym_db.RegisterMessage(PeerAddress)

EncryptedData = _reflection.GeneratedProtocolMessageType('EncryptedData', (_message.Message,), dict(
  DESCRIPTOR = _ENCRYPTEDDATA,
  __module__ = 'wallet_pb2'
  # @@protoc_insertion_point(class_scope:wallet.EncryptedData)
  ))
_sym_db.RegisterMessage(EncryptedData)

DeterministicKey = _reflection.GeneratedProtocolMessageType('DeterministicKey', (_message.Message,), dict(
  DESCRIPTOR = _DETERMINISTICKEY,
  __module__ = 'wallet_pb2'
  # @@protoc_insertion_point(class_scope:wallet.DeterministicKey)
  ))
_sym_db.RegisterMessage(DeterministicKey)

Key = _reflection.GeneratedProtocolMessageType('Key', (_message.Message,), dict(
  DESCRIPTOR = _KEY,
  __module__ = 'wallet_pb2'
  # @@protoc_insertion_point(class_scope:wallet.Key)
  ))
_sym_db.RegisterMessage(Key)

Script = _reflection.GeneratedProtocolMessageType('Script', (_message.Message,), dict(
  DESCRIPTOR = _SCRIPT,
  __module__ = 'wallet_pb2'
  # @@protoc_insertion_point(class_scope:wallet.Script)
  ))
_sym_db.RegisterMessage(Script)

TransactionInput = _reflection.GeneratedProtocolMessageType('TransactionInput', (_message.Message,), dict(
  DESCRIPTOR = _TRANSACTIONINPUT,
  __module__ = 'wallet_pb2'
  # @@protoc_insertion_point(class_scope:wallet.TransactionInput)
  ))
_sym_db.RegisterMessage(TransactionInput)

TransactionOutput = _reflection.GeneratedProtocolMessageType('TransactionOutput', (_message.Message,), dict(
  DESCRIPTOR = _TRANSACTIONOUTPUT,
  __module__ = 'wallet_pb2'
  # @@protoc_insertion_point(class_scope:wallet.TransactionOutput)
  ))
_sym_db.RegisterMessage(TransactionOutput)

TransactionConfidence = _reflection.GeneratedProtocolMessageType('TransactionConfidence', (_message.Message,), dict(
  DESCRIPTOR = _TRANSACTIONCONFIDENCE,
  __module__ = 'wallet_pb2'
  # @@protoc_insertion_point(class_scope:wallet.TransactionConfidence)
  ))
_sym_db.RegisterMessage(TransactionConfidence)

Transaction = _reflection.GeneratedProtocolMessageType('Transaction', (_message.Message,), dict(
  DESCRIPTOR = _TRANSACTION,
  __module__ = 'wallet_pb2'
  # @@protoc_insertion_point(class_scope:wallet.Transaction)
  ))
_sym_db.RegisterMessage(Transaction)

ScryptParameters = _reflection.GeneratedProtocolMessageType('ScryptParameters', (_message.Message,), dict(
  DESCRIPTOR = _SCRYPTPARAMETERS,
  __module__ = 'wallet_pb2'
  # @@protoc_insertion_point(class_scope:wallet.ScryptParameters)
  ))
_sym_db.RegisterMessage(ScryptParameters)

Extension = _reflection.GeneratedProtocolMessageType('Extension', (_message.Message,), dict(
  DESCRIPTOR = _EXTENSION,
  __module__ = 'wallet_pb2'
  # @@protoc_insertion_point(class_scope:wallet.Extension)
  ))
_sym_db.RegisterMessage(Extension)

Tag = _reflection.GeneratedProtocolMessageType('Tag', (_message.Message,), dict(
  DESCRIPTOR = _TAG,
  __module__ = 'wallet_pb2'
  # @@protoc_insertion_point(class_scope:wallet.Tag)
  ))
_sym_db.RegisterMessage(Tag)

TransactionSigner = _reflection.GeneratedProtocolMessageType('TransactionSigner', (_message.Message,), dict(
  DESCRIPTOR = _TRANSACTIONSIGNER,
  __module__ = 'wallet_pb2'
  # @@protoc_insertion_point(class_scope:wallet.TransactionSigner)
  ))
_sym_db.RegisterMessage(TransactionSigner)

Wallet = _reflection.GeneratedProtocolMessageType('Wallet', (_message.Message,), dict(
  DESCRIPTOR = _WALLET,
  __module__ = 'wallet_pb2'
  # @@protoc_insertion_point(class_scope:wallet.Wallet)
  ))
_sym_db.RegisterMessage(Wallet)

ExchangeRate = _reflection.GeneratedProtocolMessageType('ExchangeRate', (_message.Message,), dict(
  DESCRIPTOR = _EXCHANGERATE,
  __module__ = 'wallet_pb2'
  # @@protoc_insertion_point(class_scope:wallet.ExchangeRate)
  ))
_sym_db.RegisterMessage(ExchangeRate)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\023org.bitcoinj.walletB\006Protos'))
# @@protoc_insertion_point(module_scope)

# End of automatically generated code.


def process_file(filename):
    bname = os.path.basename(filename)
    try:
        f = open(filename, "rb")
        data = f.read()
    except IOError:
        e = sys.exc_info()[1]
        sys.stderr.write("%s\n" % str(e))
        return

    if "wallet" in bname or b"org.bitcoin.production" in data:
        sys.stderr.write("[WARNING] Cracking .wallet files is a very slow process, try cracking the associated .key file instead!\n")
        version = 3  # MultiBit Classic .wallet file
        # def is_wallet_file(wallet_file) from btcrecover
        wallet_file = open(filename, "rb")
        wallet_file.seek(0)
        is_valid_bitcoinj_wallet = False
        if wallet_file.read(1) == b"\x0a":  # protobuf field number 1 of type length-delimited
            network_identifier_len = ord(wallet_file.read(1))
            if 1 <= network_identifier_len < 128:
                wallet_file.seek(2 + network_identifier_len)
                c = wallet_file.read(1)
                if c and c in b"\x12\x1a":   # field number 2 or 3 of type length-delimited
                    is_valid_bitcoinj_wallet = True
        if is_valid_bitcoinj_wallet:
            pb_wallet = Wallet()
            pb_wallet.ParseFromString(data)
            if pb_wallet.encryption_type == Wallet.UNENCRYPTED:
                raise ValueError("bitcoinj wallet is not encrypted")
            if pb_wallet.encryption_type != Wallet.ENCRYPTED_SCRYPT_AES:
                raise NotImplementedError("Unsupported bitcoinj encryption type "+unicode(pb_wallet.encryption_type))
            if not pb_wallet.HasField("encryption_parameters"):
                raise ValueError("bitcoinj wallet is missing its scrypt encryption parameters")
            for key in pb_wallet.key:
                if key.type in (Key.ENCRYPTED_SCRYPT_AES, Key.DETERMINISTIC_KEY) and key.HasField("encrypted_data"):
                    encrypted_len = len(key.encrypted_data.encrypted_private_key)
                    if encrypted_len == 48:
                        # only need the final 2 encrypted blocks (half of it padding) plus the scrypt parameters
                        part_encrypted_key = key.encrypted_data.encrypted_private_key[-32:]
                        salt = pb_wallet.encryption_parameters.salt
                        n = pb_wallet.encryption_parameters.n
                        r = pb_wallet.encryption_parameters.r
                        p = pb_wallet.encryption_parameters.p
                        encrypted_data = binascii.hexlify(part_encrypted_key).decode("ascii")
                        salt = binascii.hexlify(salt).decode("ascii")
                        sys.stdout.write("%s:$multibit$%d*%s*%s*%s*%s*%s\n" % (bname, version, n, r, p, salt, encrypted_data))
                        return
            return

    version = 1  # MultiBit Classic
    pdata = b"".join(data.split())
    if len(pdata) < 64:
        sys.stderr.write("%s: Short length for a MultiBit wallet file!\n" % bname)
        return

    try:
        pdata = base64.b64decode(pdata[:64])
        if not pdata.startswith("Salted__"):
            version = 2
        if len(pdata) < 48:
            # sys.stderr.write("%s: Short length for a MultiBit wallet file!\n" % bname)
            # return
            version = 2  # MultiBit HD possibly?
    except:
        version = 2  # MultiBit HD possibly?

    if version == 1:
        encrypted_data = pdata[16:48]  # two AES blocks
        salt = pdata[8:16]
        encrypted_data = binascii.hexlify(encrypted_data).decode("ascii")
        salt = binascii.hexlify(salt).decode("ascii")
        sys.stdout.write("%s:$multibit$%d*%s*%s\n" % (bname, version, salt, encrypted_data))
        return
    else:
        version = 2
        # sanity check but not a very good one
        if "wallet" not in bname and "aes" not in bname:
            sys.stderr.write("%s: Make sure that this is a MultiBit HD wallet!\n" % bname)
        # two possibilities
        iv = data[:16]  # v0.5.0+
        block_iv = data[16:32]  # v0.5.0+
        block_noiv = data[:16]  # encrypted using hardcoded iv, < v0.5.0
        iv = binascii.hexlify(iv).decode("ascii")
        block_iv = binascii.hexlify(block_iv).decode("ascii")
        block_noiv = binascii.hexlify(block_noiv).decode("ascii")
        sys.stdout.write("%s:$multibit$%d*%s*%s*%s\n" % (bname, version, iv, block_iv, block_noiv))
        return

    f.close()

if __name__ == "__main__":
    if len(sys.argv) < 2:
        sys.stderr.write("Usage: %s [MultiBit Classic or HD wallets files (.key, mbhd.wallet.aes, .wallet)]\n" % sys.argv[0])
        sys.stderr.write("\nMultiBit Classic -> for a wallet named 'xyz', we need the xyz-data/key-backup/xyz*.key OR xyz-data/wallet-backup/xyz*.wallet file\n")
        sys.exit(-1)

    for j in range(1, len(sys.argv)):
        process_file(sys.argv[j])
