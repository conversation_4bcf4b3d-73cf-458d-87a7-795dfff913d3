	Installing <PERSON> the Ripper.

First of all, most likely you do not need to install <PERSON> the Ripper
system-wide.  Instead, after you extract the distribution archive and
possibly compile the source code (see below), you may simply enter the
"run" directory and invoke <PERSON> from there.

System-wide installation is also supported, but it is intended for use
by packagers of John for *BSD "ports", Linux distributions, etc., rather
than by end-users.  (If you're in fact preparing a package of John,
please refer to the JOHN_SYSTEMWIDE setting in src/params.h or the
Jumbo/autoconf-specific README-DISTROS.)

You may have obtained the source code or a "binary" (pre-compiled)
distribution of <PERSON> the Ripper.  On Unix-like systems, it is typical
to get the source code and compile it into "binary" executables right
on the system you intend to run John on.  On DOS and Windows, however,
it is typical to get a binary distribution which is ready for use.

The following instructions apply to the source code distribution of
<PERSON> only.  If you have a binary distribution, then there's nothing
for you to compile and you can start using <PERSON> right away.


	Requirements

The Jumbo versions requires OpenSSL 0.9.7 or later. To get all functionality
you need 1.0.1. For building, not only the run-time libs are needed but also
"devel" stuff like header files. This is often a separate package, so, e.g.,
for Ubuntu you need "libssl-dev" as well as "libssl".

Some helper tools are written in Python. A couple helper tools (vncpcap2john
and SIPdump) need libpcap. A couple of formats need extra libraries in order
to get included:

	mozilla		libnss
	krb5-18/23	libkrb5
	wowsrp		libgmp (will build without it, but is slower then)

Again, you also need e.g. libnss-dev, libkrb5-dev and libgmp-dev in order to
build.


	Compiling the sources on a Unix-like system.

Enter the directory into which you extracted the source code
distribution of John.  Enter the "src" subdirectory.
	cd src

This version of Jumbo has autoconf - that is, it supports the very common
chain of:
	./configure && make

Pass a CFLAGS variable in case you need to enable/disable compiler options
different than default. For example, here's how to configure for just AVX
on a host that is AVX2-capable (default is to find all SIMD features you've
got and enable them):
	./configure CFLAGS="-g -O2 -mno-avx2"

Note that whenever you pass CFLAGS like this, the default "-g -O2" will go
away, so add that too (like was done above).

You might want to add "-s" to make, for suppressing normal output and just
print warnings and errors. For a multi-core host you can also add e.g. "-j4"
for using 4 processes (hopefully on different cores) in parallel when building.
This can be written together, as in:
	./configure && make -sj4

That -j option does not work well with the "clean" target so if you want to
use that, do it separately:
	./configure && make -s clean && make -sj4

./configure should find and enable any extra stuff you have, including OMP,
OpenCL, and extra libraries mentioned above. The only exception is MPI
which needs the "--enable-mpi" option to be supplied to configure (nearly
all users should use the --fork option instead of MPI so wont need this). If
something is missing, it's likely installed in a non-standard location so
you'd need to pass parameters:
	./configure LDFLAGS=-L/opt/lib CFLAGS="-O2 -I/opt/include" && make -sj4

NOTE! If you pass CFLAGS like above, you probably also want to add "-O2"
since it (and "-g") will only be added automatically provided CFLAGS start
out empty. This is a feature of the autoconf macros, not specific to JtR.

If you have a broken pkg-config (eg. claiming there is no OpenSSL even though
there is one) you can disable its use within configure:
	./configure --disable-pkg-config

You can also disable certain features you don't want, eg:
	./configure --disable-openmp && make -sj4

If everything goes well, this will create the executables for John and
its related utilities under "../run/".  You can change directory to
there and start John, like this:

	cd ../run
	./john --test

Alternatively, you may copy the entire "run" directory to anywhere you
like and use John from there.


	A note on moving binaries between systems.

With the "generic" make target, certain machine hardware performance
parameters are detected at compile time.  Additionally, some OS-specific
make targets tell the C compiler to generate and optimize code for the
machine's specific CPU type (this currently applies to C compilers other
than gcc only).  If you then move the binary executable to a different
machine, you might not get the best performance or the program might
not run at all if the CPU lacks features that the C compiler assumed it
would have.  Thus, it is recommended to recompile John on each system if
you use one of these make targets.

Since Linux and *BSD distributions' packages of John typically use make
targets other than "generic" and since they typically use gcc, they are
usually not affected by this potential problem.


	OMP fallback build

Even a non-distro "native" build can gain from having OMP fallback. This
means you will have a "john" binary that has OpenMP support, but that it
automatically (quitely and seamlessly) will switch to a "john-non-omp"
binary when applicable. This gains performance and has some other benfits
too. Here's how to do it for your "home build":

	./configure --disable-openmp &&
	make -s clean && make -sj4 && mv ../run/john ../run/john-non-omp &&
	./configure CPPFLAGS='-DOMP_FALLBACK -DOMP_FALLBACK_BINARY="\"john-non-omp\""' &&
	make -s clean && make -sj4 && echo All Done

Then you just run "./john" (from run directory) as usual.


	Optimal interleaving factors (intrinsics builds)

There is a script "testparas.pl" in the src directory, that can be used
to test for optimal performance. Run it on an idle system. It will finish
with printing a full "./configure" command line to use. For most people,
it's actually overkill. Spend the time learning tricks instead!


	Optimal build on OS X

Using OS X, you can install Xcode (free) and then its "command line tools"
and after that a normal build should work fine. However, using native
gcc (which is really clang) results in suboptimal performance and some
formats are disabled due to ancient OpenSSL.  Also, clang doesn't have any
OpenMP support.

Here's how to make the best possible of your hardware. There are alternatives
that probably work fine but these instructions are for "Homebrew":

  1. Install Homebrew:
	http://mxcl.github.io/homebrew/
  2. Install Homebrew's gcc and openssl:
	brew install gcc openssl
  3. Make sure /usr/local/bin precedes /usr/bin in your $PATH
  4. Configure, possibly adding a CC option for pointing to a specific gcc:
        ./configure CC="gcc-6"
  5. Clean old files and make:
        make -s clean && make -sj4

After the above, you should be able to get an optimal build with AVX and/or
whatever extra features your CPU has got.

If you get weird problems including things like "error: unknown type name
'dispatch_block_t'" on 10.10 Yosemite, you might need to apply a patch for
the system headers (at your own risk, as always, but backups are created).
From John's "src" directory:

  sudo patch -b -N -p0 < unused/Yosemite.patch

The patch is not needed for 10.11 "El Capitan".

NOTE: The above command will create backup files. If you ever want to restore
everything as it were originally:

  cd /usr/include/dispatch
  sudo mv -vi object.h.orig object.h
  sudo mv -vi queue.h.orig queue.h
  cd -
