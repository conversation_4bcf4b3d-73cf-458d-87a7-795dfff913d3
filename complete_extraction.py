#!/usr/bin/env python3
"""
完整提取所有数据并寻找隐藏内容
"""

import struct
import os
import re

def extract_all_data_systematically():
    """系统地提取所有数据"""
    filename = "Final Exam(MS).docx"
    
    print("系统地提取所有数据...")
    
    base_size = 31
    base_sector = 30
    step = 32
    
    all_extracted_data = []
    
    with open(filename, 'rb') as f:
        f.seek(0, 2)
        file_size = f.tell()
        
        print(f"文件大小: {file_size} 字节")
        
        for i in range(1, 200):  # 检查更多位置
            size = base_size + (i-1) * step
            sector = base_sector + (i-1) * step
            file_offset = 512 + sector * 512
            
            if file_offset >= file_size:
                break
                
            f.seek(file_offset)
            data = f.read(size)
            
            if data and not all(b == 0 for b in data):
                # 尝试解码
                try:
                    text = data.decode('utf-8', errors='ignore')
                    ascii_text = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data)
                    
                    all_extracted_data.append({
                        'position': i,
                        'size': size,
                        'offset': file_offset,
                        'data': data,
                        'text': text,
                        'ascii': ascii_text
                    })
                    
                    # 检查是否包含有趣的内容
                    interesting_keywords = [
                        'password', 'key', 'secret', 'answer', 'solution', 'flag',
                        'content', 'document', 'text', 'message', 'exam', 'final',
                        'question', 'response', 'result', 'data', 'info'
                    ]
                    
                    text_lower = text.lower()
                    ascii_lower = ascii_text.lower()
                    
                    found_keywords = []
                    for keyword in interesting_keywords:
                        if keyword in text_lower or keyword in ascii_lower:
                            found_keywords.append(keyword)
                    
                    if found_keywords or len(text.strip()) > 50:
                        print(f"\n位置 {i} (偏移 {file_offset}):")
                        print(f"  大小: {size} 字节")
                        if found_keywords:
                            print(f"  关键词: {', '.join(found_keywords)}")
                        print(f"  文本: {text[:100]}...")
                        print(f"  ASCII: {ascii_text[:100]}...")
                        
                except Exception as e:
                    pass
    
    print(f"\n总共提取了 {len(all_extracted_data)} 个数据块")
    
    return all_extracted_data

def analyze_for_hidden_content(data_blocks):
    """分析隐藏内容"""
    print("\n" + "="*60)
    print("分析隐藏内容...")
    
    # 合并所有文本
    all_text = ""
    all_ascii = ""
    
    for block in data_blocks:
        all_text += block['text']
        all_ascii += block['ascii']
    
    # 保存完整内容
    with open("complete_extracted.txt", "w", encoding='utf-8') as f:
        f.write(all_text)
    
    with open("complete_extracted_ascii.txt", "w", encoding='utf-8') as f:
        f.write(all_ascii)
    
    print("已保存完整内容到:")
    print("  - complete_extracted.txt (UTF-8)")
    print("  - complete_extracted_ascii.txt (ASCII)")
    
    # 查找模式
    print("\n查找文本模式:")
    
    # 查找重复的字符串
    words = re.findall(r'\b[a-zA-Z]{3,}\b', all_text)
    word_count = {}
    for word in words:
        word_count[word] = word_count.get(word, 0) + 1
    
    # 显示最常见的单词
    common_words = sorted(word_count.items(), key=lambda x: x[1], reverse=True)[:20]
    if common_words:
        print("最常见的单词:")
        for word, count in common_words:
            if count > 1 and len(word) > 3:
                print(f"  {word}: {count} 次")
    
    # 查找可能的Base64编码
    base64_pattern = r'[A-Za-z0-9+/]{20,}={0,2}'
    base64_matches = re.findall(base64_pattern, all_text)
    if base64_matches:
        print(f"\n发现可能的Base64编码 ({len(base64_matches)} 个):")
        for i, match in enumerate(base64_matches[:5]):
            print(f"  {i+1}: {match[:50]}...")
            
            # 尝试解码
            try:
                import base64
                decoded = base64.b64decode(match)
                decoded_text = decoded.decode('utf-8', errors='ignore')
                if decoded_text.strip():
                    print(f"      解码: {decoded_text[:100]}")
            except:
                pass
    
    # 查找十六进制模式
    hex_pattern = r'[0-9a-fA-F]{16,}'
    hex_matches = re.findall(hex_pattern, all_text)
    if hex_matches:
        print(f"\n发现十六进制字符串 ({len(hex_matches)} 个):")
        for i, match in enumerate(hex_matches[:5]):
            print(f"  {i+1}: {match[:50]}...")
            
            # 尝试解码
            try:
                if len(match) % 2 == 0:
                    decoded = bytes.fromhex(match)
                    decoded_text = decoded.decode('utf-8', errors='ignore')
                    if decoded_text.strip():
                        print(f"      解码: {decoded_text[:100]}")
            except:
                pass

def search_for_final_answer():
    """搜索最终答案"""
    print("\n" + "="*60)
    print("搜索最终答案...")
    
    try:
        # 读取所有提取的内容
        with open("complete_extracted.txt", "r", encoding='utf-8') as f:
            content = f.read()
        
        with open("complete_extracted_ascii.txt", "r", encoding='utf-8') as f:
            ascii_content = f.read()
        
        # 查找可能包含答案的模式
        answer_patterns = [
            r'answer[:\s]*([^\n\r]{5,50})',
            r'solution[:\s]*([^\n\r]{5,50})',
            r'password[:\s]*([^\n\r]{5,50})',
            r'key[:\s]*([^\n\r]{5,50})',
            r'flag[:\s]*([^\n\r]{5,50})',
            r'result[:\s]*([^\n\r]{5,50})',
            r'final[:\s]*([^\n\r]{5,50})',
        ]
        
        print("搜索答案模式:")
        for pattern in answer_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                print(f"  模式 '{pattern}' 找到:")
                for match in matches[:3]:
                    print(f"    {match.strip()}")
        
        # 查找连续的可读文本
        print("\n查找连续的可读文本:")
        
        # 分割成句子
        sentences = re.split(r'[.!?]+', content)
        meaningful_sentences = []
        
        for sentence in sentences:
            clean_sentence = sentence.strip()
            if len(clean_sentence) > 20 and ' ' in clean_sentence:
                meaningful_sentences.append(clean_sentence)
        
        if meaningful_sentences:
            print(f"发现 {len(meaningful_sentences)} 个有意义的句子:")
            for i, sentence in enumerate(meaningful_sentences[:10]):
                print(f"  {i+1}: {sentence[:100]}...")
        
        # 查找数字序列
        print("\n查找数字序列:")
        number_sequences = re.findall(r'\d{4,}', content)
        if number_sequences:
            print("发现数字序列:")
            for seq in set(number_sequences):
                if len(seq) >= 6:
                    print(f"  {seq}")
        
        # 查找特殊字符组合
        print("\n查找特殊模式:")
        
        # 查找可能的密码格式
        password_patterns = [
            r'[A-Z][a-z]+\d+',  # 大写字母开头+小写字母+数字
            r'\d+[A-Za-z]+\d+',  # 数字+字母+数字
            r'[A-Za-z]{3,}\d{2,}',  # 字母+数字
        ]
        
        for pattern in password_patterns:
            matches = re.findall(pattern, content)
            if matches:
                print(f"  密码模式 '{pattern}':")
                for match in set(matches):
                    if 4 <= len(match) <= 20:
                        print(f"    {match}")
                        
    except Exception as e:
        print(f"搜索失败: {e}")

def main():
    data_blocks = extract_all_data_systematically()
    analyze_for_hidden_content(data_blocks)
    search_for_final_answer()

if __name__ == "__main__":
    main()
