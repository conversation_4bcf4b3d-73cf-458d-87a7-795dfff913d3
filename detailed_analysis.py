#!/usr/bin/env python3
"""
详细分析OLE文件内容
"""

import struct
import os

def analyze_pattern():
    """分析发现的模式"""
    filename = "Final Exam(MS).docx"
    
    print("详细分析模式...")
    
    # 我们发现了一个有趣的模式：
    # 条目1: 大小31, 扇区30
    # 条目2: 大小63, 扇区62  
    # 条目3: 大小95, 扇区94 (ABCDEFGHIJKLMNOP)
    # 条目4: 大小127, 扇区126 (abcdefghijklmnop)
    
    # 这看起来像是一个数学序列！
    # 大小: 31, 63, 95, 127 (每次+32)
    # 扇区: 30, 62, 94, 126 (每次+32, 但比大小少1)
    
    print("发现的模式:")
    print("条目1: 大小=31,  扇区=30  (31-1=30)")
    print("条目2: 大小=63,  扇区=62  (63-1=62)")  
    print("条目3: 大小=95,  扇区=94  (95-1=94)")
    print("条目4: 大小=127, 扇区=126 (127-1=126)")
    print()
    print("数学模式:")
    print("大小序列: 31, 63, 95, 127 (公差=32)")
    print("扇区序列: 30, 62, 94, 126 (公差=32)")
    print()
    
    # 让我们检查这些位置的实际数据
    with open(filename, 'rb') as f:
        print("检查实际数据内容:")
        
        positions = [(31, 30), (63, 62), (95, 94), (127, 126)]
        
        for i, (size, sector) in enumerate(positions, 1):
            print(f"\n条目{i} (大小={size}, 扇区={sector}):")
            
            # 计算文件中的实际位置
            file_offset = 512 + sector * 512
            f.seek(file_offset)
            data = f.read(min(size, 100))  # 读取最多100字节
            
            print(f"文件偏移: {file_offset}")
            print(f"十六进制: {data.hex()}")
            
            # 尝试解码为ASCII
            try:
                ascii_text = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data)
                print(f"ASCII: {ascii_text}")
            except:
                print("ASCII: 无法解码")
            
            # 检查是否有可读文本
            try:
                utf8_text = data.decode('utf-8', errors='ignore')
                if utf8_text.strip():
                    print(f"UTF-8: {utf8_text}")
            except:
                pass

def check_mathematical_sequence():
    """检查是否存在更多的数学序列"""
    print("\n" + "="*60)
    print("检查扩展的数学序列...")
    
    # 基于发现的模式，下一个应该是:
    # 条目5: 大小=159, 扇区=158
    # 条目6: 大小=191, 扇区=190
    # 等等...
    
    filename = "Final Exam(MS).docx"
    
    with open(filename, 'rb') as f:
        # 检查文件大小
        f.seek(0, 2)
        file_size = f.tell()
        print(f"文件总大小: {file_size} 字节")
        
        # 预测更多的位置
        base_size = 31
        base_sector = 30
        step = 32
        
        print("\n预测的序列位置:")
        for i in range(1, 20):  # 检查前20个
            predicted_size = base_size + (i-1) * step
            predicted_sector = base_sector + (i-1) * step
            file_offset = 512 + predicted_sector * 512
            
            if file_offset < file_size:
                f.seek(file_offset)
                data = f.read(min(predicted_size, 50))
                
                # 检查是否有有意义的数据
                if data and not all(b == 0 for b in data):
                    ascii_text = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[:30])
                    print(f"位置{i:2d}: 大小={predicted_size:3d}, 扇区={predicted_sector:3d}, 偏移={file_offset:6d} -> {ascii_text}")

def extract_all_meaningful_data():
    """提取所有有意义的数据"""
    print("\n" + "="*60)
    print("提取所有发现的数据...")
    
    filename = "Final Exam(MS).docx"
    
    # 基于模式提取数据
    base_size = 31
    base_sector = 30
    step = 32
    
    all_data = b""
    
    with open(filename, 'rb') as f:
        f.seek(0, 2)
        file_size = f.tell()
        
        for i in range(1, 50):  # 检查更多位置
            size = base_size + (i-1) * step
            sector = base_sector + (i-1) * step
            file_offset = 512 + sector * 512
            
            if file_offset >= file_size:
                break
                
            f.seek(file_offset)
            data = f.read(size)
            
            if data and not all(b == 0 for b in data):
                print(f"提取位置{i}: {len(data)} 字节")
                all_data += data
    
    # 保存所有提取的数据
    with open("extracted_all_data.bin", "wb") as f:
        f.write(all_data)
    
    print(f"\n总共提取了 {len(all_data)} 字节数据")
    print("已保存到: extracted_all_data.bin")
    
    # 尝试解码
    print("\n尝试解码提取的数据:")
    
    # ASCII解码
    ascii_text = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in all_data)
    print(f"ASCII (前200字符): {ascii_text[:200]}")
    
    # UTF-8解码
    try:
        utf8_text = all_data.decode('utf-8', errors='ignore')
        if utf8_text.strip():
            print(f"UTF-8 (前200字符): {utf8_text[:200]}")
    except:
        pass
    
    # 查找可能的文本模式
    print("\n查找文本模式:")
    text_chunks = []
    current_chunk = ""
    
    for byte in all_data:
        if 32 <= byte <= 126:  # 可打印ASCII
            current_chunk += chr(byte)
        else:
            if len(current_chunk) >= 5:  # 至少5个字符的文本块
                text_chunks.append(current_chunk)
            current_chunk = ""
    
    if current_chunk and len(current_chunk) >= 5:
        text_chunks.append(current_chunk)
    
    print(f"找到 {len(text_chunks)} 个文本块:")
    for i, chunk in enumerate(text_chunks[:10]):  # 显示前10个
        print(f"  {i+1}: {chunk}")

def main():
    analyze_pattern()
    check_mathematical_sequence()
    extract_all_meaningful_data()

if __name__ == "__main__":
    main()
