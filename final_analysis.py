#!/usr/bin/env python3
"""
最终分析 - 重新检查所有数据寻找密码线索
"""

import struct
import re
import string

def analyze_all_data_blocks():
    """分析所有数据块寻找模式"""
    print("重新分析所有数据块...")
    
    filename = "Final Exam(MS).docx"
    
    # 基于我们发现的模式提取所有数据
    base_size = 31
    base_sector = 30
    step = 32
    
    all_data = []
    
    with open(filename, 'rb') as f:
        f.seek(0, 2)
        file_size = f.tell()
        
        for i in range(1, 50):  # 检查前50个位置
            size = base_size + (i-1) * step
            sector = base_sector + (i-1) * step
            file_offset = 512 + sector * 512
            
            if file_offset >= file_size:
                break
                
            f.seek(file_offset)
            data = f.read(size)
            
            if data and not all(b == 0 for b in data):
                all_data.append({
                    'position': i,
                    'size': size,
                    'data': data,
                    'ascii': ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data)
                })
    
    print(f"提取了 {len(all_data)} 个数据块")
    
    # 分析每个数据块
    for block in all_data:
        print(f"\n位置 {block['position']} (大小 {block['size']}):")
        print(f"ASCII: {block['ascii'][:50]}...")
        
        # 查找可能的密码模式
        data = block['data']
        ascii_text = block['ascii']
        
        # 查找连续的字母数字字符串
        words = re.findall(r'[a-zA-Z0-9]{3,}', ascii_text)
        if words:
            print(f"  单词: {words[:10]}")
        
        # 查找可能的密码格式
        password_patterns = [
            r'[a-zA-Z]+-[a-zA-Z0-9]+',  # 字母-字母数字
            r'[A-Z][a-z]+\d+',          # 大写字母开头+小写+数字
            r'\d+[A-Za-z]+',            # 数字+字母
            r'[a-zA-Z]{2,6}\d{1,3}',    # 字母+数字
        ]
        
        for pattern in password_patterns:
            matches = re.findall(pattern, ascii_text)
            if matches:
                print(f"  模式 '{pattern}': {matches}")

def look_for_hidden_text():
    """寻找隐藏的文本"""
    print("\n寻找隐藏的文本...")
    
    # 读取我们之前提取的所有内容
    try:
        with open("complete_extracted.txt", "r", encoding='utf-8') as f:
            content = f.read()
    except:
        print("无法读取complete_extracted.txt")
        return
    
    # 查找可能被忽略的文本
    print("查找可能的隐藏文本:")
    
    # 查找所有可能的单词
    words = re.findall(r'[a-zA-Z]{3,}', content)
    word_freq = {}
    for word in words:
        word_freq[word.lower()] = word_freq.get(word.lower(), 0) + 1
    
    # 显示频率最高的单词
    sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
    print("最常见的单词:")
    for word, freq in sorted_words[:20]:
        if freq > 1:
            print(f"  {word}: {freq} 次")
    
    # 查找可能的密码相关单词
    password_keywords = ['pass', 'word', 'key', 'secret', 'code', 'lock', 'open', 'access', 'login', 'auth']
    print("\n密码相关单词:")
    for word, freq in sorted_words:
        if any(keyword in word.lower() for keyword in password_keywords):
            print(f"  {word}: {freq} 次")

def analyze_first_block_deeply():
    """深度分析第一个数据块"""
    print("\n深度分析第一个数据块...")
    
    filename = "Final Exam(MS).docx"
    
    with open(filename, 'rb') as f:
        # 第一个数据块：偏移15872，大小31
        f.seek(15872)
        data = f.read(31)
        
        print(f"第一个数据块 (31字节):")
        print(f"十六进制: {data.hex()}")
        print(f"字节数组: {list(data)}")
        
        # 尝试不同的解码方式
        print("\n解码尝试:")
        
        # 1. 直接ASCII
        ascii_text = ''.join(chr(b) if 32 <= b <= 126 else f'\\x{b:02x}' for b in data)
        print(f"ASCII: {ascii_text}")
        
        # 2. 尝试UTF-8
        try:
            utf8_text = data.decode('utf-8', errors='ignore')
            print(f"UTF-8: {utf8_text}")
        except:
            pass
        
        # 3. 尝试不同的字符编码
        encodings = ['latin1', 'cp1252', 'iso-8859-1']
        for encoding in encodings:
            try:
                text = data.decode(encoding, errors='ignore')
                if text.isprintable() and len(text.strip()) > 3:
                    print(f"{encoding}: {text}")
            except:
                pass
        
        # 4. 分析字节模式
        print(f"\n字节分析:")
        print(f"前7字节 (可能是密码): {data[:7]}")
        print(f"前7字节ASCII: {''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[:7])}")
        
        # 5. 尝试XOR解码
        print(f"\nXOR解码尝试:")
        for key in [0x20, 0x30, 0x40, 0x50, 0x60, 0x70]:  # 常见的XOR键
            decoded = bytes(b ^ key for b in data[:7])
            try:
                text = decoded.decode('ascii', errors='ignore')
                if text.isprintable() and len(text.strip()) > 3:
                    print(f"  XOR {key:02x}: {text}")
            except:
                pass
        
        # 6. 尝试移位解码
        print(f"\n移位解码尝试:")
        for shift in [1, 2, 3, 13, 25]:  # 包括ROT13
            shifted = ""
            for b in data[:7]:
                if 65 <= b <= 90:  # A-Z
                    shifted += chr((b - 65 + shift) % 26 + 65)
                elif 97 <= b <= 122:  # a-z
                    shifted += chr((b - 97 + shift) % 26 + 97)
                elif 32 <= b <= 126:
                    shifted += chr(b)
                else:
                    shifted += '.'
            
            if len(shifted.strip()) > 3:
                print(f"  移位 {shift:2d}: {shifted}")

def try_reverse_engineering():
    """尝试逆向工程方法"""
    print("\n尝试逆向工程方法...")
    
    # 基于我们的发现，第一行是 "a-dTJO2"
    # 让我们尝试理解这可能代表什么
    
    first_line = "a-dTJO2"
    print(f"分析: {first_line}")
    
    # 可能的解释
    interpretations = {
        "直接密码": first_line,
        "去连字符": first_line.replace('-', ''),
        "替换连字符为0": first_line.replace('-', '0'),
        "替换连字符为1": first_line.replace('-', '1'),
        "大写": first_line.upper(),
        "小写": first_line.lower(),
        "反转": first_line[::-1],
        "只取字母": ''.join(c for c in first_line if c.isalpha()),
        "只取数字": ''.join(c for c in first_line if c.isdigit()),
        "ASCII值": ''.join(str(ord(c)) for c in first_line),
    }
    
    print("可能的解释:")
    for desc, value in interpretations.items():
        print(f"  {desc}: {value}")
    
    # 尝试基于位置的解释
    print(f"\n基于位置的分析:")
    print(f"位置1的大小是31，扇区是30")
    print(f"31的二进制: {bin(31)} (2^5 - 1)")
    print(f"30的二进制: {bin(30)}")
    
    # 可能密码是基于这些数字的
    number_based = [
        "31", "30", "3130", "3031",
        str(bin(31))[2:], str(bin(30))[2:],  # 二进制
        hex(31)[2:], hex(30)[2:],  # 十六进制
        f"{31}-{30}", f"{30}-{31}",
    ]
    
    print("基于数字的密码候选:")
    for pwd in number_based:
        print(f"  {pwd}")
    
    return list(interpretations.values()) + number_based

def final_password_attempt():
    """最终密码尝试"""
    print("\n最终密码尝试...")
    
    # 收集所有可能的密码
    all_passwords = []
    
    # 1. 基于逆向工程的密码
    reverse_passwords = try_reverse_engineering()
    all_passwords.extend(reverse_passwords)
    
    # 2. 基于数学模式的密码
    math_passwords = [
        "31", "63", "95", "127",  # 我们发现的序列
        "30", "62", "94", "126",  # 对应的扇区
        "1f", "3f", "5f", "7f",   # 十六进制
        "11111", "111111", "1111111", "11111111",  # 二进制
    ]
    all_passwords.extend(math_passwords)
    
    # 3. 常见密码
    common_passwords = [
        "password", "123456", "admin", "test", "guest",
        "password123", "admin123", "test123",
        "final", "exam", "document", "word", "office",
        "microsoft", "docx", "file", "secret", "key"
    ]
    all_passwords.extend(common_passwords)
    
    # 4. 基于文件名的密码
    filename_passwords = [
        "final", "exam", "ms", "finalexam", "examfinal",
        "Final", "Exam", "MS", "FinalExam", "ExamFinal",
        "FINAL", "EXAM", "FINALEXAM", "EXAMFINAL"
    ]
    all_passwords.extend(filename_passwords)
    
    # 去重
    unique_passwords = list(set(all_passwords))
    
    print(f"准备尝试 {len(unique_passwords)} 个最终密码候选")
    
    # 保存到文件
    with open("final_passwords.txt", "w", encoding='utf-8') as f:
        for pwd in unique_passwords:
            f.write(pwd + '\n')
    
    print("最终密码列表已保存到: final_passwords.txt")
    
    # 尝试解密
    filename = "Final Exam(MS).docx"
    
    try:
        import msoffcrypto
        import io
        
        for i, password in enumerate(unique_passwords):
            try:
                print(f"尝试 {i+1}/{len(unique_passwords)}: {password}")
                
                with open(filename, "rb") as f:
                    office_file = msoffcrypto.OfficeFile(f)
                    office_file.load_key(password=password)
                    
                    decrypted = io.BytesIO()
                    office_file.decrypt(decrypted)
                    
                    # 成功解密
                    output_filename = f"decrypted_final_{password.replace('/', '_').replace(':', '_').replace('-', '_')}.docx"
                    with open(output_filename, "wb") as out:
                        out.write(decrypted.getvalue())
                    
                    print(f"\n🎉 最终破解成功！密码是: {password}")
                    print(f"解密文件保存为: {output_filename}")
                    
                    return password, output_filename
                    
            except Exception as e:
                continue
        
        print("最终尝试也失败了")
        return None, None
        
    except ImportError:
        print("msoffcrypto模块不可用")
        return None, None

def main():
    print("最终分析和密码破解尝试")
    print("=" * 60)
    
    # 重新分析所有数据
    analyze_all_data_blocks()
    
    # 寻找隐藏文本
    look_for_hidden_text()
    
    # 深度分析第一个数据块
    analyze_first_block_deeply()
    
    # 最终密码尝试
    password, decrypted_file = final_password_attempt()
    
    if password:
        print(f"\n🎉 成功破解！密码是: {password}")
        
        # 分析解密后的文件
        if decrypted_file:
            try:
                import zipfile
                
                with zipfile.ZipFile(decrypted_file, 'r') as docx:
                    if 'word/document.xml' in docx.namelist():
                        with docx.open('word/document.xml') as doc_xml:
                            content = doc_xml.read().decode('utf-8')
                            
                            # 提取文本
                            import re
                            text_matches = re.findall(r'<w:t[^>]*>([^<]+)</w:t>', content)
                            
                            if text_matches:
                                full_text = ' '.join(text_matches)
                                print(f"\n📄 文档内容:")
                                print("=" * 60)
                                print(full_text)
                                print("=" * 60)
            except:
                pass
    else:
        print("\n❌ 所有尝试都失败了")
        print("\n这个文档可能:")
        print("1. 使用了非常复杂的密码")
        print("2. 使用了更强的加密算法")
        print("3. 需要专业的密码破解工具")
        print("4. 密码线索在我们没有分析到的地方")

if __name__ == "__main__":
    main()
