## rule: switch last two chars of word with each other
## limits: words greater or equal 10 chars length
## example: johnnoble ---> johnnobel
## extras: experienced effective cases

>A k l
>A k u
>A k c

## rule: permuted switch of chars of word with each other
## limits: char positions 0 to 9
## example: peterbishop ---> petrebishop
## extras: none

*01
*02
*03
*04
*05
*06
*07
*08
*09
*12
*13
*14
*15
*16
*17
*18
*19
*23
*24
*25
*26
*27
*28
*29
*34
*35
*36
*37
*38
*39
*45
*46
*47
*48
*49
*56
*57
*58
*59
*67
*68
*69
*78
*79
*89

## rule: increase char
## limits: positions 0 to 9
## example: nina555 ---> nina666
## extras: none

+0
+1
+2
+3
+4
+5
+6
+7
+8
+9

+0+1
+1+2
+2+3
+3+4
+4+5
+5+6
+6+7
+7+8
+8+9

+0+0
+1+1
+2+2
+3+3
+4+4
+5+5
+6+6
+7+7
+8+8
+9+9

+0+1+2
+1+2+3
+2+3+4
+3+4+5
+4+5+6
+5+6+7
+6+7+8
+7+8+9

+0+0+0
+1+1+1
+2+2+2
+3+3+3
+4+4+4
+5+5+5
+6+6+6
+7+7+7
+8+8+8
+9+9+9

+0+1+2+3
+1+2+3+4
+2+3+4+5
+3+4+5+6
+4+5+6+7
+5+6+7+8
+6+7+8+9

+0+0+0+0
+1+1+1+1
+2+2+2+2
+3+3+3+3
+4+4+4+4
+5+5+5+5
+6+6+6+6
+7+7+7+7
+8+8+8+8
+9+9+9+9

## rule: decrease char
## limits: positions 0 to 9
## example: astrid0816 ---> astrid0815
## extras: none

-0
-1
-2
-3
-4
-5
-6
-7
-8
-9

-0-1
-1-2
-2-3
-3-4
-4-5
-5-6
-6-7
-7-8
-8-9

-0-0
-1-1
-2-2
-3-3
-4-4
-5-5
-6-6
-7-7
-8-8
-9-9

-0-1-2
-1-2-3
-2-3-4
-3-4-5
-4-5-6
-5-6-7
-6-7-8
-7-8-9

-0-0-0
-1-1-1
-2-2-2
-3-3-3
-4-4-4
-5-5-5
-6-6-6
-7-7-7
-8-8-8
-9-9-9

-0-1-2-3
-1-2-3-4
-2-3-4-5
-3-4-5-6
-4-5-6-7
-5-6-7-8
-6-7-8-9

-0-0-0-0
-1-1-1-1
-2-2-2-2
-3-3-3-3
-4-4-4-4
-5-5-5-5
-6-6-6-6
-7-7-7-7
-8-8-8-8
-9-9-9-9
