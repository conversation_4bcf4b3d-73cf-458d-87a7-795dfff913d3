#!/usr/bin/env python3
"""
尝试解密Office文档
"""

import subprocess
import os
import sys

def try_msoffcrypto():
    """尝试使用msoffcrypto-tool"""
    print("尝试使用msoffcrypto-tool解密...")
    
    # 首先检查是否安装了msoffcrypto-tool
    try:
        result = subprocess.run(['pip', 'show', 'msoffcrypto-tool'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("msoffcrypto-tool未安装，正在安装...")
            subprocess.run(['pip', 'install', 'msoffcrypto-tool'], check=True)
    except Exception as e:
        print(f"安装msoffcrypto-tool失败: {e}")
        return False
    
    # 读取密码列表
    passwords = []
    try:
        with open("possible_passwords.txt", "r", encoding='utf-8') as f:
            passwords = [line.strip() for line in f if line.strip()]
    except:
        # 如果文件不存在，使用默认密码
        passwords = [
            "a-dTJO2", "adTJO2", "ADTJO2", "2OJTd-a",
            "finala-dTJO2", "exama-dTJO2", "a-dTJO2123", "passa-dTJO2"
        ]
    
    print(f"尝试 {len(passwords)} 个密码...")
    
    filename = "Final Exam(MS).docx"
    
    for i, password in enumerate(passwords[:50]):  # 尝试前50个最有希望的密码
        try:
            print(f"尝试密码 {i+1}: {password}")
            
            # 使用msoffcrypto-tool尝试解密
            cmd = [
                'python', '-c',
                f'''
import msoffcrypto
import io

with open("{filename}", "rb") as f:
    office_file = msoffcrypto.OfficeFile(f)
    office_file.load_key(password="{password}")
    
    decrypted = io.BytesIO()
    office_file.decrypt(decrypted)
    
    with open("decrypted_{i+1}_{password.replace("/", "_").replace(":", "_")}.docx", "wb") as out:
        out.write(decrypted.getvalue())
    
    print(f"成功解密！密码是: {password}")
'''
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"✓ 成功！密码是: {password}")
                print(result.stdout)
                return password
            else:
                if "Invalid password" not in result.stderr and result.stderr:
                    print(f"  错误: {result.stderr[:100]}")
                
        except subprocess.TimeoutExpired:
            print(f"  超时")
        except Exception as e:
            print(f"  异常: {e}")
    
    print("所有密码都尝试失败")
    return None

def try_office2john():
    """尝试使用office2john提取哈希"""
    print("\n尝试使用office2john提取哈希...")
    
    filename = "Final Exam(MS).docx"
    
    try:
        # 尝试使用office2john（如果可用）
        result = subprocess.run(['office2john', filename], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("成功提取哈希:")
            print(result.stdout)
            
            # 保存哈希到文件
            with open("document_hash.txt", "w") as f:
                f.write(result.stdout)
            
            return result.stdout
        else:
            print(f"office2john失败: {result.stderr}")
            
    except FileNotFoundError:
        print("office2john未找到")
    except Exception as e:
        print(f"office2john异常: {e}")
    
    return None

def try_simple_python_decrypt():
    """使用简单的Python方法尝试解密"""
    print("\n使用Python直接尝试解密...")
    
    try:
        import msoffcrypto
        import io
        
        filename = "Final Exam(MS).docx"
        
        # 读取密码列表
        passwords = [
            "a-dTJO2", "adTJO2", "ADTJO2", "2OJTd-a",
            "finala-dTJO2", "exama-dTJO2", "a-dTJO2123", "passa-dTJO2",
            "a-dtjo2", "A-DTJO2", "password", "123456", "admin", "test",
            "final", "exam", "document", "key", "secret"
        ]
        
        with open(filename, "rb") as f:
            office_file = msoffcrypto.OfficeFile(f)
            
            for i, password in enumerate(passwords):
                try:
                    print(f"尝试密码: {password}")
                    
                    # 重新打开文件
                    f.seek(0)
                    office_file = msoffcrypto.OfficeFile(f)
                    office_file.load_key(password=password)
                    
                    decrypted = io.BytesIO()
                    office_file.decrypt(decrypted)
                    
                    # 成功解密
                    output_filename = f"decrypted_success_{password.replace('/', '_').replace(':', '_')}.docx"
                    with open(output_filename, "wb") as out:
                        out.write(decrypted.getvalue())
                    
                    print(f"✓ 成功解密！密码是: {password}")
                    print(f"解密文件保存为: {output_filename}")
                    
                    return password, output_filename
                    
                except Exception as e:
                    if "Invalid password" not in str(e):
                        print(f"  错误: {e}")
                    continue
        
        print("所有密码都失败了")
        return None, None
        
    except ImportError:
        print("msoffcrypto模块未安装")
        return None, None
    except Exception as e:
        print(f"解密过程出错: {e}")
        return None, None

def install_and_try():
    """安装必要的工具并尝试解密"""
    print("安装必要的工具...")
    
    try:
        # 安装msoffcrypto-tool
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'msoffcrypto-tool'], 
                      check=True, capture_output=True)
        print("✓ msoffcrypto-tool安装成功")
        
        # 尝试解密
        return try_simple_python_decrypt()
        
    except Exception as e:
        print(f"安装失败: {e}")
        return None, None

def main():
    print("开始尝试解密Office文档...")
    
    # 检查文件是否存在
    filename = "Final Exam(MS).docx"
    if not os.path.exists(filename):
        print(f"错误：文件 {filename} 不存在")
        return
    
    # 方法1：直接尝试Python解密
    password, decrypted_file = try_simple_python_decrypt()
    
    if password:
        print(f"\n🎉 解密成功！")
        print(f"密码: {password}")
        print(f"解密文件: {decrypted_file}")
        return
    
    # 方法2：安装工具后再试
    print("\n尝试安装工具后解密...")
    password, decrypted_file = install_and_try()
    
    if password:
        print(f"\n🎉 解密成功！")
        print(f"密码: {password}")
        print(f"解密文件: {decrypted_file}")
        return
    
    # 方法3：尝试提取哈希
    hash_result = try_office2john()
    
    if not password:
        print("\n❌ 解密失败")
        print("可能的原因:")
        print("1. 密码不在我们的候选列表中")
        print("2. 文档使用了更强的加密")
        print("3. 文档可能损坏")
        print("\n建议:")
        print("1. 尝试更多密码组合")
        print("2. 使用专业的密码破解工具")
        print("3. 检查是否有其他线索")

if __name__ == "__main__":
    main()
