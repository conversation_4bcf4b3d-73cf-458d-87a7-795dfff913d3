#!/usr/bin/env python3
"""
简化版的office2john工具，用于提取Office文档的密码哈希
"""

import struct
import sys
import os
import hashlib

def extract_office_hash(filename):
    """提取Office文档的密码哈希"""
    print(f"分析文件: {filename}")
    
    with open(filename, 'rb') as f:
        # 读取文件头
        header = f.read(512)
        
        # 检查OLE签名
        if header[:8] != b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1':
            print("错误：不是有效的OLE文件")
            return None
        
        print("检测到OLE文件格式")
        
        # 解析基本头部信息
        sector_size = 2 ** struct.unpack('<H', header[30:32])[0]
        dir_first_sector = struct.unpack('<L', header[52:56])[0]
        
        print(f"扇区大小: {sector_size}")
        print(f"目录第一扇区: {dir_first_sector}")
        
        # 尝试查找加密信息
        encryption_info = find_encryption_info(f, sector_size, dir_first_sector)
        
        if encryption_info:
            print("找到加密信息")
            return format_hash(filename, encryption_info)
        else:
            print("未找到加密信息")
            return None

def find_encryption_info(f, sector_size, dir_first_sector):
    """查找加密信息"""
    try:
        # 读取目录扇区
        f.seek(512 + dir_first_sector * sector_size)
        dir_data = f.read(sector_size)
        
        # 查找加密相关的流
        encryption_streams = []
        
        # 解析目录条目（每个128字节）
        for i in range(0, sector_size, 128):
            if i + 128 <= len(dir_data):
                entry = dir_data[i:i+128]
                
                # 获取名称
                name_utf16 = entry[:64]
                name_len = struct.unpack('<H', entry[64:66])[0]
                
                if name_len > 0:
                    try:
                        name = name_utf16[:name_len-2].decode('utf-16le')
                        
                        # 查找加密相关的流
                        if any(keyword in name.lower() for keyword in 
                               ['encryption', 'encryptioninfo', 'version', '0encryptioninfo', '6dataspacemap']):
                            
                            entry_type = entry[66]
                            start_sector = struct.unpack('<L', entry[116:120])[0]
                            size = struct.unpack('<L', entry[120:124])[0]
                            
                            print(f"找到加密流: {name}, 大小: {size}, 起始扇区: {start_sector}")
                            
                            # 读取流数据
                            if size > 0 and start_sector != 0xFFFFFFFE:
                                stream_data = read_stream_data(f, start_sector, size, sector_size)
                                encryption_streams.append({
                                    'name': name,
                                    'data': stream_data,
                                    'size': size
                                })
                    except:
                        continue
        
        # 如果找到加密流，分析它们
        if encryption_streams:
            return analyze_encryption_streams(encryption_streams)
        
        # 如果没有找到标准的加密流，尝试查找其他模式
        return search_for_encryption_patterns(f)
        
    except Exception as e:
        print(f"查找加密信息时出错: {e}")
        return None

def read_stream_data(f, start_sector, size, sector_size):
    """读取流数据"""
    try:
        f.seek(512 + start_sector * sector_size)
        return f.read(min(size, sector_size))
    except:
        return b''

def analyze_encryption_streams(streams):
    """分析加密流"""
    for stream in streams:
        data = stream['data']
        if len(data) >= 16:
            # 尝试解析加密信息
            print(f"分析流: {stream['name']}")
            print(f"数据长度: {len(data)}")
            print(f"前32字节: {data[:32].hex()}")
            
            # 查找可能的版本信息
            if len(data) >= 8:
                version_info = struct.unpack('<LL', data[:8])
                print(f"可能的版本信息: {version_info}")
                
                # 根据版本信息判断Office版本
                if version_info[0] in [0x02, 0x03, 0x04]:
                    print(f"检测到Office版本: {version_info[0]}")
                    
                    # 提取更多信息
                    if len(data) >= 32:
                        return {
                            'version': version_info[0],
                            'data': data,
                            'salt': data[8:24] if len(data) >= 24 else b'',
                            'hash': data[24:40] if len(data) >= 40 else b''
                        }
    
    return None

def search_for_encryption_patterns(f):
    """搜索加密模式"""
    print("搜索加密模式...")
    
    # 回到文件开头
    f.seek(0)
    file_size = f.seek(0, 2)
    f.seek(0)
    
    # 搜索已知的加密签名
    encryption_signatures = [
        b'\x02\x00\x00\x00',  # Office 2007
        b'\x03\x00\x00\x00',  # Office 2010
        b'\x04\x00\x00\x00',  # Office 2013+
    ]
    
    chunk_size = 8192
    for offset in range(0, min(file_size, 1024*1024), chunk_size):  # 搜索前1MB
        f.seek(offset)
        chunk = f.read(chunk_size)
        
        for sig in encryption_signatures:
            pos = chunk.find(sig)
            if pos != -1:
                print(f"在偏移 {offset + pos} 找到加密签名: {sig.hex()}")
                
                # 读取更多数据
                f.seek(offset + pos)
                enc_data = f.read(256)
                
                return {
                    'version': struct.unpack('<L', sig)[0],
                    'data': enc_data,
                    'offset': offset + pos
                }
    
    return None

def format_hash(filename, encryption_info):
    """格式化哈希输出"""
    if not encryption_info:
        return None
    
    version = encryption_info.get('version', 0)
    data = encryption_info.get('data', b'')
    
    if version == 2:
        office_version = "2007"
    elif version == 3:
        office_version = "2010"
    elif version == 4:
        office_version = "2013"
    else:
        office_version = "unknown"
    
    print(f"检测到Office {office_version}")
    
    # 创建简化的哈希格式
    if len(data) >= 32:
        # 提取关键信息
        salt = data[8:24] if len(data) >= 24 else data[4:20]
        encrypted_verifier = data[24:40] if len(data) >= 40 else data[20:36]
        
        # 格式化为John the Ripper格式
        hash_str = f"{filename}:$office$*{office_version}*20*128*16*{salt.hex()}*{encrypted_verifier.hex()}*{data[:16].hex()}"
        
        return hash_str
    
    return None

def main():
    if len(sys.argv) != 2:
        print("用法: python simple_office2john.py <office_file>")
        sys.exit(1)
    
    filename = sys.argv[1]
    
    if not os.path.exists(filename):
        print(f"错误：文件 {filename} 不存在")
        sys.exit(1)
    
    hash_result = extract_office_hash(filename)
    
    if hash_result:
        print("\n提取的哈希:")
        print(hash_result)
        
        # 保存到文件
        with open("hash.txt", "w") as f:
            f.write(hash_result + "\n")
        
        print("\n哈希已保存到 hash.txt")
        print("现在可以使用 John the Ripper 或 Hashcat 来破解密码")
        print("例如: john --wordlist=wordlist.txt hash.txt")
    else:
        print("无法提取哈希")

if __name__ == "__main__":
    main()
