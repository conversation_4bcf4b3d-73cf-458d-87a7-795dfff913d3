#!/usr/bin/env python3
"""
分析可能的密码和关键信息
"""

import re
import base64
import hashlib

def analyze_potential_passwords():
    """分析潜在的密码"""
    print("分析潜在的密码...")
    
    # 从提取的内容中找到的可能密码
    potential_passwords = [
        "a-dTJO2",  # 第一行，看起来像密码格式
        "Gfc2",
        "Poc3", 
        "Lfi8",
        "3SV6",
        "0J03",
        "5g52",
        "4fT21",
        "3Lfi8",
        "7lp4",
        "kcw60",
        "qu35",
        "H0J03",
        "2J:Tw*",
        "9U2J",
        "3DisS",
        "5=$",
        "3@t",
        "2r",
        "3G%",
        "5g52",
        "2l::",
        "5=$",
        "2/",
        "0[}",
        "9\\:",
        "2-",
        "5'",
        "49)",
        "8R,",
        "55\\",
        "2V;",
        "7x4",
        "8 |",
        "0j1",
        "5&6",
        "1X",
        "2Oj",
        "5N",
        "4fT21",
        "3%",
        "6#}",
        "3z",
        "8Y_",
        "1CPK",
        "0qup",
        "9Vva",
        "3X",
        "6<",
        "05z",
        "1ݚ",
        "9Փ",
        "0r",
        "3Ҵ",
        "8n",
        "6xcn",
        "1[",
        "8μ",
        "6A",
        "5Z9",
        "8m",
        "19]",
        "4:X",
        "2wS",
        "5W",
        "68z",
        "7*",
        "2f"
    ]
    
    print(f"发现 {len(potential_passwords)} 个潜在密码:")
    for i, pwd in enumerate(potential_passwords[:20]):  # 显示前20个
        print(f"  {i+1:2d}: {pwd}")
    
    return potential_passwords

def test_common_transformations(passwords):
    """测试常见的密码变换"""
    print("\n测试常见的密码变换...")
    
    transformations = []
    
    for pwd in passwords[:10]:  # 测试前10个
        # 原始密码
        transformations.append(pwd)
        
        # 大小写变换
        transformations.append(pwd.upper())
        transformations.append(pwd.lower())
        
        # 反转
        transformations.append(pwd[::-1])
        
        # 去除特殊字符
        clean_pwd = re.sub(r'[^a-zA-Z0-9]', '', pwd)
        if clean_pwd and clean_pwd != pwd:
            transformations.append(clean_pwd)
        
        # 只保留字母
        letters_only = re.sub(r'[^a-zA-Z]', '', pwd)
        if letters_only and len(letters_only) >= 3:
            transformations.append(letters_only)
        
        # 只保留数字
        numbers_only = re.sub(r'[^0-9]', '', pwd)
        if numbers_only and len(numbers_only) >= 2:
            transformations.append(numbers_only)
    
    # 去重
    unique_transformations = list(set(transformations))
    
    print(f"生成了 {len(unique_transformations)} 个变换后的密码:")
    for i, pwd in enumerate(unique_transformations[:30]):
        print(f"  {i+1:2d}: {pwd}")
    
    return unique_transformations

def analyze_first_line_pattern():
    """分析第一行的模式"""
    print("\n分析第一行模式...")
    
    first_line = "a-dTJO2"
    print(f"第一行: {first_line}")
    
    # 分析模式
    print("模式分析:")
    print(f"  长度: {len(first_line)}")
    print(f"  字符: {list(first_line)}")
    print(f"  ASCII值: {[ord(c) for c in first_line]}")
    
    # 可能的解释
    print("\n可能的解释:")
    print("  1. 直接密码: a-dTJO2")
    print("  2. 去除连字符: adTJO2")
    print("  3. 大写: A-DTJO2")
    print("  4. 小写: a-dtjo2")
    print("  5. 反转: 2OJTd-a")
    
    # 检查是否是某种编码
    print("\n编码检查:")
    
    # Base64检查
    try:
        # 尝试不同的填充
        for padding in ['', '=', '==']:
            test_str = first_line.replace('-', '') + padding
            try:
                decoded = base64.b64decode(test_str)
                print(f"  Base64 ('{test_str}'): {decoded}")
            except:
                pass
    except:
        pass
    
    # 十六进制检查
    hex_chars = re.sub(r'[^0-9a-fA-F]', '', first_line)
    if len(hex_chars) >= 4 and len(hex_chars) % 2 == 0:
        try:
            decoded = bytes.fromhex(hex_chars)
            print(f"  十六进制 ('{hex_chars}'): {decoded}")
        except:
            pass
    
    # ROT13检查
    import codecs
    try:
        rot13 = codecs.encode(first_line, 'rot13')
        print(f"  ROT13: {rot13}")
    except:
        pass

def search_for_document_content():
    """搜索文档内容"""
    print("\n" + "="*60)
    print("搜索可能的文档内容...")
    
    # 读取ASCII版本，可能更容易找到隐藏的文本
    try:
        with open("complete_extracted_ascii.txt", "r", encoding='utf-8') as f:
            ascii_content = f.read()
        
        # 查找连续的可读文本
        print("查找连续的可读文本块:")
        
        # 分割成行
        lines = ascii_content.split('\n')
        
        for i, line in enumerate(lines):
            # 查找包含多个单词的行
            words = re.findall(r'[a-zA-Z]{3,}', line)
            if len(words) >= 3:  # 至少3个单词
                print(f"  行{i+1}: {line[:100]}")
        
        # 查找可能的句子
        print("\n查找可能的句子:")
        sentences = re.findall(r'[A-Z][a-z\s]{10,}[.!?]', ascii_content)
        for i, sentence in enumerate(sentences[:5]):
            print(f"  {i+1}: {sentence}")
        
        # 查找重复的模式
        print("\n查找重复的模式:")
        
        # 查找3-6字符的重复模式
        for length in range(3, 7):
            patterns = {}
            for i in range(len(ascii_content) - length):
                pattern = ascii_content[i:i+length]
                if pattern.isalnum():  # 只考虑字母数字
                    patterns[pattern] = patterns.get(pattern, 0) + 1
            
            # 显示出现次数最多的模式
            common_patterns = sorted(patterns.items(), key=lambda x: x[1], reverse=True)[:5]
            if common_patterns and common_patterns[0][1] > 2:
                print(f"  长度{length}的模式:")
                for pattern, count in common_patterns:
                    if count > 2:
                        print(f"    '{pattern}': {count} 次")
                        
    except Exception as e:
        print(f"搜索失败: {e}")

def try_password_combinations():
    """尝试密码组合"""
    print("\n" + "="*60)
    print("尝试密码组合...")
    
    # 基于发现的模式，尝试一些组合
    base_passwords = ["a-dTJO2", "adTJO2", "ADTJO2", "2OJTd-a"]
    
    # 常见的密码后缀
    suffixes = ["", "123", "2024", "2025", "!", "@", "#", "$"]
    
    # 常见的密码前缀
    prefixes = ["", "pass", "pwd", "key", "final", "exam"]
    
    combinations = []
    
    for base in base_passwords:
        for prefix in prefixes:
            for suffix in suffixes:
                combo = prefix + base + suffix
                if 4 <= len(combo) <= 20:  # 合理的密码长度
                    combinations.append(combo)
    
    print(f"生成了 {len(combinations)} 个密码组合:")
    for i, combo in enumerate(combinations[:30]):
        print(f"  {i+1:2d}: {combo}")
    
    return combinations

def main():
    passwords = analyze_potential_passwords()
    transformations = test_common_transformations(passwords)
    analyze_first_line_pattern()
    search_for_document_content()
    combinations = try_password_combinations()
    
    # 保存所有可能的密码到文件
    all_passwords = list(set(passwords + transformations + combinations))
    
    with open("possible_passwords.txt", "w", encoding='utf-8') as f:
        for pwd in all_passwords:
            f.write(pwd + '\n')
    
    print(f"\n总共生成了 {len(all_passwords)} 个可能的密码")
    print("已保存到: possible_passwords.txt")
    
    print("\n最有希望的密码候选:")
    priority_passwords = [
        "a-dTJO2",
        "adTJO2", 
        "ADTJO2",
        "2OJTd-a",
        "finala-dTJO2",
        "exama-dTJO2",
        "a-dTJO2123",
        "passa-dTJO2"
    ]
    
    for i, pwd in enumerate(priority_passwords):
        print(f"  {i+1}: {pwd}")

if __name__ == "__main__":
    main()
