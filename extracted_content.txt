nt name="TermInfo">

<xs:complexType>

<xs:sequence>

<xs:element ref="pc:TermName" minOccurs="0"></xs:element>

<xs:element ref="pc:TermId" minOccurs="0"></xs:element>

</xs:sequence>

</xs:complexType>

</xs:element>

<xs:element name="TermName" type="xs:string"></xs:element>

<xs:element name="TermId" type="xs:string"></xs:element>

</xs:schema>

</ct:contentTypeSchema>                                                                                                                                        