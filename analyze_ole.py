#!/usr/bin/env python3
"""
OLE文件分析工具
用于分析Microsoft Compound Document格式的文件
"""

import struct
import sys
import os

def read_ole_header(filename):
    """读取OLE文件头信息"""
    with open(filename, 'rb') as f:
        # 读取前512字节（一个扇区）
        header = f.read(512)
        
        # 检查OLE签名
        signature = header[:8]
        if signature != b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1':
            print("错误：不是有效的OLE文件")
            return None
            
        print("OLE文件头分析:")
        print(f"签名: {signature.hex()}")
        
        # 解析头部信息
        minor_version = struct.unpack('<H', header[24:26])[0]
        major_version = struct.unpack('<H', header[26:28])[0]
        byte_order = struct.unpack('<H', header[28:30])[0]
        sector_size = struct.unpack('<H', header[30:32])[0]
        mini_sector_size = struct.unpack('<H', header[32:34])[0]
        
        print(f"次版本号: {minor_version}")
        print(f"主版本号: {major_version}")
        print(f"字节序: {byte_order}")
        print(f"扇区大小: {2**sector_size} 字节")
        print(f"迷你扇区大小: {2**mini_sector_size} 字节")
        
        # 目录扇区数量
        dir_sectors = struct.unpack('<L', header[44:48])[0]
        print(f"目录扇区数量: {dir_sectors}")
        
        # FAT扇区数量
        fat_sectors = struct.unpack('<L', header[48:52])[0]
        print(f"FAT扇区数量: {fat_sectors}")
        
        # 目录第一个扇区
        dir_first_sector = struct.unpack('<L', header[52:56])[0]
        print(f"目录第一个扇区: {dir_first_sector}")
        
        return {
            'sector_size': 2**sector_size,
            'mini_sector_size': 2**mini_sector_size,
            'dir_sectors': dir_sectors,
            'fat_sectors': fat_sectors,
            'dir_first_sector': dir_first_sector,
            'header': header
        }

def read_fat_table(filename, header_info):
    """读取FAT表"""
    sector_size = header_info['sector_size']
    fat_sectors = header_info['fat_sectors']
    
    with open(filename, 'rb') as f:
        # 读取FAT扇区位置（在头部的109个位置）
        fat_locations = []
        header = header_info['header']
        
        for i in range(109):
            offset = 76 + i * 4
            if offset < 512:
                sector_id = struct.unpack('<L', header[offset:offset+4])[0]
                if sector_id != 0xFFFFFFFE:  # FREESECT
                    fat_locations.append(sector_id)
        
        print(f"FAT扇区位置: {fat_locations[:10]}...")  # 只显示前10个
        
        # 读取FAT表内容
        fat_table = []
        for sector_id in fat_locations:
            f.seek(512 + sector_id * sector_size)
            sector_data = f.read(sector_size)
            
            # 每个FAT条目是4字节
            for i in range(0, sector_size, 4):
                if i + 4 <= len(sector_data):
                    entry = struct.unpack('<L', sector_data[i:i+4])[0]
                    fat_table.append(entry)
        
        return fat_table

def read_directory_entries(filename, header_info, fat_table):
    """读取目录条目"""
    sector_size = header_info['sector_size']
    dir_first_sector = header_info['dir_first_sector']
    
    entries = []
    current_sector = dir_first_sector
    
    with open(filename, 'rb') as f:
        while current_sector != 0xFFFFFFFE:  # ENDOFCHAIN
            f.seek(512 + current_sector * sector_size)
            sector_data = f.read(sector_size)
            
            # 每个目录条目是128字节
            for i in range(0, sector_size, 128):
                if i + 128 <= len(sector_data):
                    entry_data = sector_data[i:i+128]
                    
                    # 解析目录条目
                    name_utf16 = entry_data[:64]
                    name_len = struct.unpack('<H', entry_data[64:66])[0]
                    
                    if name_len > 0:
                        # 转换UTF-16名称
                        try:
                            name = name_utf16[:name_len-2].decode('utf-16le')
                        except:
                            name = "无法解码"
                        
                        entry_type = entry_data[66]
                        start_sector = struct.unpack('<L', entry_data[116:120])[0]
                        size = struct.unpack('<L', entry_data[120:124])[0]
                        
                        entries.append({
                            'name': name,
                            'type': entry_type,
                            'start_sector': start_sector,
                            'size': size
                        })
            
            # 获取下一个扇区
            if current_sector < len(fat_table):
                current_sector = fat_table[current_sector]
            else:
                break
    
    return entries

def extract_stream_data(filename, header_info, fat_table, start_sector, size):
    """提取流数据"""
    sector_size = header_info['sector_size']
    data = b''
    current_sector = start_sector
    bytes_read = 0
    
    with open(filename, 'rb') as f:
        while current_sector != 0xFFFFFFFE and bytes_read < size:
            f.seek(512 + current_sector * sector_size)
            sector_data = f.read(sector_size)
            
            bytes_to_read = min(sector_size, size - bytes_read)
            data += sector_data[:bytes_to_read]
            bytes_read += bytes_to_read
            
            # 获取下一个扇区
            if current_sector < len(fat_table):
                current_sector = fat_table[current_sector]
            else:
                break
    
    return data

def main():
    filename = "Final Exam(MS).docx"
    
    if not os.path.exists(filename):
        print(f"错误：文件 {filename} 不存在")
        return
    
    print("开始分析OLE文件...")
    
    # 读取头部信息
    header_info = read_ole_header(filename)
    if not header_info:
        return
    
    print("\n" + "="*50)
    
    # 读取FAT表
    print("读取FAT表...")
    fat_table = read_fat_table(filename, header_info)
    print(f"FAT表大小: {len(fat_table)} 条目")
    
    print("\n" + "="*50)
    
    # 读取目录条目
    print("读取目录结构...")
    entries = read_directory_entries(filename, header_info, fat_table)
    
    print(f"找到 {len(entries)} 个目录条目:")
    for i, entry in enumerate(entries):
        type_name = {0: "未知", 1: "存储", 2: "流", 5: "根条目"}.get(entry['type'], "其他")
        print(f"{i+1:2d}. {entry['name']:<30} 类型:{type_name:<8} 大小:{entry['size']:<10} 起始扇区:{entry['start_sector']}")
    
    print("\n" + "="*50)
    
    # 尝试提取一些重要的流
    for entry in entries:
        if entry['type'] == 2 and entry['size'] > 0:  # 流类型
            if any(keyword in entry['name'].lower() for keyword in ['encryption', 'version', 'info', 'data']):
                print(f"\n提取流: {entry['name']}")
                try:
                    stream_data = extract_stream_data(filename, header_info, fat_table, 
                                                    entry['start_sector'], entry['size'])
                    
                    # 保存到文件
                    safe_name = entry['name'].replace('/', '_').replace('\\', '_')
                    output_file = f"extracted_{safe_name}.bin"
                    with open(output_file, 'wb') as f:
                        f.write(stream_data)
                    
                    print(f"已保存到: {output_file}")
                    
                    # 显示前100字节的十六进制
                    print(f"前100字节 (十六进制):")
                    hex_data = stream_data[:100].hex()
                    for i in range(0, len(hex_data), 32):
                        print(f"  {hex_data[i:i+32]}")
                    
                    # 尝试显示可打印字符
                    printable = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in stream_data[:100])
                    print(f"可打印字符: {printable}")
                    
                except Exception as e:
                    print(f"提取失败: {e}")

if __name__ == "__main__":
    main()
